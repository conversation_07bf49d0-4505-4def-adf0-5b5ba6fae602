// app/leagues/index.tsx - Leagues main page
import { useAuth } from '../../contexts/AuthContext';
import { Redirect } from 'expo-router';
import LeaguesScreen from '../../screens/LeaguesScreen';

export default function LeaguesPage() {
  const { user, loading, checkOnboardingStatus } = useAuth();

  if (loading) {
    return null; // Or loading component
  }

  if (!user) {
    return <Redirect href="/login" />;
  }

  // Check if user needs onboarding
  if (!checkOnboardingStatus()) {
    return <Redirect href="/onboarding" />;
  }

  return <LeaguesScreen />;
}
