// app/leagues/[id].tsx - League detail page
import { useAuth } from '../../contexts/AuthContext';
import { Redirect } from 'expo-router';
import LeagueDetailScreen from '../../screens/LeagueDetailScreen';

export default function LeagueDetailPage() {
  const { user, loading, checkOnboardingStatus } = useAuth();

  if (loading) {
    return null; // Or loading component
  }

  if (!user) {
    return <Redirect href="/login" />;
  }

  // Check if user needs onboarding
  if (!checkOnboardingStatus()) {
    return <Redirect href="/onboarding" />;
  }

  return <LeagueDetailScreen />;
}
