// app/api/stats+api.ts - Stats API route for Expo Router
import { prisma } from "../../lib/prisma";

// Mock stats data for development
const mockOverviewStats = {
  totalUsers: 150,
  totalPlayers: 16,
  totalTeams: 4,
  totalFixtures: 30,
  activeUsers: 120,
};

const mockPlayerStats = [
  {
    id: 'att1',
    name: '<PERSON><PERSON><PERSON>',
    position: 'ATK',
    team: { name: 'Espérance Sportive de Tunis', abbr: 'EST' },
    totalPoints: 85,
    goalsScored: 12,
    assists: 4,
    selectedBy: 45.2,
  },
  {
    id: 'att4',
    name: '<PERSON><PERSON><PERSON>',
    position: 'AT<PERSON>',
    team: { name: 'Stade Tunisien', abbr: 'ST' },
    totalPoints: 90,
    goalsScored: 14,
    assists: 2,
    selectedBy: 38.7,
  },
];

const mockTeamStats = [
  {
    id: 'est',
    name: 'Espérance Sportive de Tunis',
    abbr: 'EST',
    logo: '/logos/est.webp',
    playerCount: 4,
    totalPoints: 251,
    avgPoints: 62.75,
  },
  {
    id: 'st',
    name: 'Stade Tunisien',
    abbr: 'ST',
    logo: '/logos/st.webp',
    playerCount: 4,
    totalPoints: 240,
    avgPoints: 60.0,
  },
];

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const type = searchParams.get("type");
    const gw = searchParams.get("gw");

    // If no GW is specified, use the active gameweek ID
    const activeGW: string | null = gw;

    switch (type) {
      case "overview":
        return getOverviewStats();
      case "players":
        return getPlayerStats(activeGW);
      case "teams":
        return getTeamStats();
      case "transfers":
        return getTransferStats(activeGW);
      case "dream-team":
        return getDreamTeam(activeGW);
      default:
        return getOverviewStats();
    }
  } catch (error) {
    console.error("Error fetching stats:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function getOverviewStats() {
  try {
    const [totalUsers, totalPlayers, totalTeams, totalFixtures, activeUsers] =
      await Promise.all([
        prisma.user.count(),
        prisma.player.count(),
        prisma.team.count(),
        prisma.fixture.count(),
        prisma.user.count({
          where: {
            gwSheets: {
              some: {},
            },
          },
        }),
      ]);

    const stats = {
      totalUsers: totalUsers || mockOverviewStats.totalUsers,
      totalPlayers: totalPlayers || mockOverviewStats.totalPlayers,
      totalTeams: totalTeams || mockOverviewStats.totalTeams,
      totalFixtures: totalFixtures || mockOverviewStats.totalFixtures,
      activeUsers: activeUsers || mockOverviewStats.activeUsers,
    };

    return Response.json(stats, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error("Error fetching overview stats:", error);
    return Response.json(mockOverviewStats, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }
}

async function getPlayerStats(activeGW: string | null) {
  try {
    const players = await prisma.player.findMany({
      include: {
        team: {
          select: {
            name: true,
            abbr: true,
          },
        },
        totalStats: {
          where: {
            season: "2025",
          },
        },
      },
      orderBy: {
        totalStats: {
          _count: "desc",
        },
      },
      take: 20,
    });

    if (players.length === 0) {
      return Response.json(mockPlayerStats, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });
    }

    const playerStats = players.map((player) => ({
      id: player.id,
      name: player.name,
      position: player.position,
      team: player.team,
      totalPoints: player.totalStats[0]?.points || 0,
      goalsScored: player.totalStats[0]?.goalsScored || 0,
      assists: player.totalStats[0]?.assists || 0,
      selectedBy: Math.random() * 50, // Mock selection percentage
    }));

    return Response.json(playerStats, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error("Error fetching player stats:", error);
    return Response.json(mockPlayerStats, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }
}

async function getTeamStats() {
  try {
    const teams = await prisma.team.findMany({
      include: {
        players: {
          include: {
            totalStats: {
              where: {
                season: "2025",
              },
            },
          },
        },
        _count: {
          select: {
            players: true,
          },
        },
      },
    });

    if (teams.length === 0) {
      return Response.json(mockTeamStats, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });
    }

    const teamStats = teams.map((team) => {
      const totalPoints = team.players.reduce(
        (sum, player) =>
          sum + (player.totalStats.find((s) => s.season === "2025")?.points || 0),
        0
      );
      const avgPoints =
        team.players.length > 0 ? totalPoints / team.players.length : 0;

      return {
        id: team.id,
        name: team.name,
        abbr: team.abbr,
        logo: team.logo,
        playerCount: team._count.players,
        totalPoints,
        avgPoints: Math.round(avgPoints * 100) / 100,
      };
    });

    return Response.json(
      teamStats.sort((a, b) => b.totalPoints - a.totalPoints),
      {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  } catch (error) {
    console.error("Error fetching team stats:", error);
    return Response.json(mockTeamStats, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }
}

async function getTransferStats(activeGW: string | null) {
  // Mock transfer stats for now
  const mockTransferStats = {
    mostTransferredIn: mockPlayerStats[0],
    mostTransferredOut: mockPlayerStats[1],
    totalTransfers: 1250,
    avgTransfersPerUser: 2.3,
  };

  return Response.json(mockTransferStats, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

async function getDreamTeam(activeGW: string | null) {
  // Mock dream team for now
  const mockDreamTeam = {
    formation: "3-4-3",
    players: mockPlayerStats.slice(0, 11),
    totalPoints: 850,
    captain: mockPlayerStats[0],
  };

  return Response.json(mockDreamTeam, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
