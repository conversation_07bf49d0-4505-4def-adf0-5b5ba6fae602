// app/api/userGWSheet/[userId]/[GW]+api.ts - UserGWSheet API route for Expo Router
import { prisma } from "../../../../lib/prisma";
import { getAuthUser } from "../../../../lib/auth";
import { getCurrentPeriod, TRANSFER_RULES, CHIP } from "../../../../lib/utils";

export async function GET(
  req: Request,
  { userId, GW }: { userId: string; GW: string }
) {
  try {
    const authUser = await getAuthUser();
    const currentUserId = authUser?.id;
    
    if (!authUser || !currentUserId) {
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const userGWSheet = await prisma.userGWSheet.findFirst({
      where: {
        userId,
        GW,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        gameWeek: {
          select: {
            id: true,
            GW: true,
            season: true,
            isActive: true,
          },
        },
      },
    });

    if (!userGWSheet) {
      return Response.json(
        { error: "GW Sheet not found" },
        { status: 404 }
      );
    }

    return Response.json(userGWSheet, {
      headers: {
        'Compression': 'gzip',
        'Cache-Control': 'no-store',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error("Error fetching user GW sheet:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: Request,
  { userId, GW }: { userId: string; GW: string }
) {
  const startTime = Date.now();
  console.log(
    `[TEAM_SAVE] Starting team save request for user ${userId}, GW ${GW} at ${new Date().toISOString()}`
  );

  try {
    const authUser = await getAuthUser();
    const currentUserId = authUser?.id;

    if (!authUser || !currentUserId) {
      console.log(`[TEAM_SAVE] ERROR: Authentication required`);
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    if (currentUserId !== userId) {
      console.log(`[TEAM_SAVE] ERROR: Unauthorized access attempt`);
      return Response.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const {
      starters,
      subs,
      captainId,
      viceCaptainId,
      firstSubId,
      secondSubId,
      thirdSubId,
      fourthSubId,
      totalPoints = 0,
      transfersMade = 0,
    } = body;

    console.log(`[TEAM_SAVE] Request data received - Starters: ${starters?.length}, Subs: ${subs?.length}`);

    // Validation
    if (!starters || !subs || starters.length !== 11 || subs.length !== 4) {
      console.log(`[TEAM_SAVE] ERROR: Invalid team composition`);
      return Response.json(
        { error: "Invalid team composition. Must have 11 starters and 4 subs." },
        { status: 400 }
      );
    }

    if (!captainId || !viceCaptainId) {
      console.log(`[TEAM_SAVE] ERROR: Captain and vice-captain are required`);
      return Response.json(
        { error: "Captain and vice-captain are required" },
        { status: 400 }
      );
    }

    // Verify all players exist and get their prices
    const allPlayerIds = [...starters, ...subs];
    const players = await prisma.player.findMany({
      where: {
        id: {
          in: allPlayerIds,
        },
      },
      select: {
        id: true,
        currentPrice: true,
        position: true,
        teamId: true,
      },
    });

    if (players.length !== allPlayerIds.length) {
      console.log(`[TEAM_SAVE] ERROR: Some players not found`);
      return Response.json(
        { error: "Some players not found" },
        { status: 400 }
      );
    }

    // Calculate team value
    const teamValue = players.reduce(
      (sum, player) => sum + Number(player.currentPrice),
      0
    );

    if (teamValue > TRANSFER_RULES.MAX_BUDGET) {
      console.log(`[TEAM_SAVE] ERROR: Team value exceeds budget`);
      return Response.json(
        { error: "Team value exceeds budget" },
        { status: 400 }
      );
    }

    // Validate team composition (positions, team limits, etc.)
    const positionCounts = players.reduce((acc, player) => {
      acc[player.position] = (acc[player.position] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const teamCounts = players.reduce((acc, player) => {
      acc[player.teamId] = (acc[player.teamId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Check position requirements
    const requiredPositions = { GK: 2, DEF: 5, MID: 5, ATK: 3 };
    for (const [position, required] of Object.entries(requiredPositions)) {
      if ((positionCounts[position] || 0) !== required) {
        console.log(`[TEAM_SAVE] ERROR: Invalid ${position} count`);
        return Response.json(
          { error: `Invalid ${position} count: ${positionCounts[position] || 0}/${required} required` },
          { status: 400 }
        );
      }
    }

    // Check team limits
    for (const [teamId, count] of Object.entries(teamCounts)) {
      if (count > TRANSFER_RULES.MAX_PLAYERS_PER_TEAM) {
        console.log(`[TEAM_SAVE] ERROR: Too many players from team ${teamId}`);
        return Response.json(
          { error: `Too many players from one team (${count}/${TRANSFER_RULES.MAX_PLAYERS_PER_TEAM} max)` },
          { status: 400 }
        );
      }
    }

    console.log(`[TEAM_SAVE] Validation passed - Creating/updating team`);

    // Check if sheet exists
    const existingSheet = await prisma.userGWSheet.findFirst({
      where: {
        userId,
        GW,
      },
    });

    let userGWSheet;
    if (existingSheet) {
      // Update existing sheet
      userGWSheet = await prisma.userGWSheet.update({
        where: {
          id: existingSheet.id,
        },
        data: {
          starters,
          subs,
          captainId,
          viceCaptainId,
          firstSubId,
          secondSubId,
          thirdSubId,
          fourthSubId,
          totalPoints,
          transfersMade,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          gameWeek: {
            select: {
              id: true,
              GW: true,
              season: true,
              isActive: true,
            },
          },
        },
      });
    } else {
      // Create new sheet
      userGWSheet = await prisma.userGWSheet.create({
        data: {
          userId,
          GW,
          starters,
          subs,
          captainId,
          viceCaptainId,
          firstSubId,
          secondSubId,
          thirdSubId,
          fourthSubId,
          totalPoints,
          transfersMade,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          gameWeek: {
            select: {
              id: true,
              GW: true,
              season: true,
              isActive: true,
            },
          },
        },
      });
    }

    const duration = Date.now() - startTime;
    console.log(`[TEAM_SAVE] SUCCESS: Team saved in ${duration}ms`);

    return Response.json(userGWSheet, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[TEAM_SAVE] ERROR after ${duration}ms:`, error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
