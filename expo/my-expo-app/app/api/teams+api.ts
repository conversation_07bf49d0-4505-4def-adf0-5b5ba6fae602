// app/api/teams+api.ts - Teams API route for Expo Router
import { prisma } from "../../lib/prisma";
import { CacheService } from "../../lib/cache";

// Mock teams data for development
const mockTeams = [
  {
    id: 'est',
    name: 'Espérance Sportive de Tunis',
    abbr: 'EST',
    logo: '/logos/est.webp',
    jersey: '/jerseys/est.webp',
  },
  {
    id: 'ca',
    name: 'Club Africain',
    abbr: 'CA',
    logo: '/logos/ca.webp',
    jersey: '/jerseys/ca.webp',
  },
  {
    id: 'css',
    name: 'Club Sportif Sfaxien',
    abbr: 'CSS',
    logo: '/logos/css.webp',
    jersey: '/jerseys/css.webp',
  },
  {
    id: 'st',
    name: 'Stade Tunisien',
    abbr: 'ST',
    logo: '/logos/st.webp',
    jersey: '/jerseys/st.webp',
  },
];

export async function GET() {
  try {
    const teams = await CacheService.getTeams(async () => {
      const dbTeams = await prisma.team.findMany({
        select: {
          id: true,
          name: true,
          abbr: true,
          logo: true,
          jersey: true,
        },
        orderBy: {
          name: "asc",
        },
      });

      // If no teams in database, return mock data
      if (dbTeams.length === 0) {
        return mockTeams;
      }

      return dbTeams;
    });

    return Response.json(teams, {
      status: 200,
      headers: {
        'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=172800',
        'X-Cache-Status': 'OPTIMIZED',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Teams API Error:', error);
    return Response.json(
      { error: 'Failed to fetch teams' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
