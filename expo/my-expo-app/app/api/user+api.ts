// app/api/user+api.ts - User API route for Expo Router
import { prisma } from "../../lib/prisma";
import { getAuthUser } from "../../lib/auth";
import { TRANSFER_RULES } from "../../lib/utils";

// Mock chip initialization function
async function initializeUserChips(userId: string, period: string) {
  const chips = [
    { chip: "WILDCARD", period, isUsed: false },
    { chip: "BENCH_BOOST", period, isUsed: false },
    { chip: "TRIPLE_CAPTAIN", period, isUsed: false },
    { chip: "FREE_HIT", period, isUsed: false },
  ];

  for (const chipData of chips) {
    await prisma.userChip.create({
      data: {
        userId,
        chip: chipData.chip as any,
        period: chipData.period,
        season: "2025",
        isUsed: chipData.isUsed,
      },
    });
  }
}

export async function POST(request: Request) {
  const startTime = Date.now();
  console.log(
    `[USER_CREATE] Starting user creation request at ${new Date().toISOString()}`
  );

  try {
    const {
      userId,
      email,
      name,
      phone,
      country = "TN",
      state,
      favoriteClub,
      starters,
      subs,
      captainId,
      viceCaptainId,
      GW_number,
      firstGameWeek,
    } = await request.json();

    console.log(
      `[USER_CREATE] User data received - Email: ${email}, Country: ${country}, FavoriteClub: ${favoriteClub}`
    );

    const authUser = await getAuthUser();

    if (!authUser) {
      console.log(
        `[USER_CREATE] ERROR: Authentication required - No auth user found`
      );
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    if (!userId) {
      console.log(`[USER_CREATE] ERROR: User ID is required`);
      return Response.json({ error: "User ID is required" }, { status: 400 });
    }

    if (authUser.id !== userId) {
      console.log(
        `[USER_CREATE] ERROR: Unauthorized - Auth user ID: ${authUser.id}, Requested user ID: ${userId}`
      );
      return Response.json({ error: "Unauthorized" }, { status: 403 });
    }

    console.log(`[USER_CREATE] Authentication successful for user: ${userId}`);

    // Validate team composition
    if (!starters || !subs || starters.length !== 11 || subs.length !== 4) {
      console.log(`[USER_CREATE] ERROR: Invalid team composition`);
      return Response.json(
        { error: "Invalid team composition" },
        { status: 400 }
      );
    }

    if (!captainId || !viceCaptainId) {
      console.log(`[USER_CREATE] ERROR: Captain and vice-captain are required`);
      return Response.json(
        { error: "Captain and vice-captain are required" },
        { status: 400 }
      );
    }

    // Verify all players exist and calculate team value
    const allPlayerIds = [...starters, ...subs];
    const players = await prisma.player.findMany({
      where: {
        id: {
          in: allPlayerIds,
        },
      },
      select: {
        id: true,
        currentPrice: true,
        position: true,
        teamId: true,
      },
    });

    if (players.length !== allPlayerIds.length) {
      console.log(`[USER_CREATE] ERROR: Some players not found`);
      return Response.json(
        { error: "Some players not found" },
        { status: 400 }
      );
    }

    const teamValue = players.reduce(
      (sum, player) => sum + Number(player.currentPrice),
      0
    );

    if (teamValue > TRANSFER_RULES.MAX_BUDGET) {
      console.log(
        `[USER_CREATE] ERROR: Team value exceeds budget. Team value: ${teamValue}, Budget: ${TRANSFER_RULES.MAX_BUDGET}`
      );
      return Response.json(
        { error: "Team value exceeds budget" },
        { status: 400 }
      );
    }

    const moneyLeft = TRANSFER_RULES.MAX_BUDGET - teamValue;

    console.log(`[USER_CREATE] Team value validation passed - Creating user`);

    // Create the user
    const user = await prisma.user.create({
      data: {
        id: userId,
        email,
        name,
        phone,
        country,
        favoriteClub,
        transfersLeft: 0,
        totalPoints: 0,
        hasOnboarded: true,
        isPhoneVerified: false,
        moneyLeft,
        firstGameWeek,
      },
    });

    console.log(
      `[USER_CREATE] SUCCESS: Created user ${userId} with email ${email}`
    );

    // Initialize user chips for both periods
    try {
      await initializeUserChips(userId, "1");
      await initializeUserChips(userId, "2");
      console.log(`[USER_CREATE] SUCCESS: Initialized chips for user ${userId}`);
    } catch (chipError) {
      console.error(`[USER_CREATE] WARNING: Failed to initialize chips:`, chipError);
    }

    // Create initial team sheet
    try {
      const userGWSheet = await prisma.userGWSheet.create({
        data: {
          userId,
          GW: firstGameWeek,
          starters,
          subs,
          captainId,
          viceCaptainId,
          firstSubId: subs[0],
          secondSubId: subs[1],
          thirdSubId: subs[2],
          fourthSubId: subs[3],
          totalPoints: 0,
          transfersMade: 0,
        },
      });

      console.log(
        `[USER_CREATE] SUCCESS: Created initial team sheet for GW ${firstGameWeek}`
      );
    } catch (sheetError) {
      console.error(`[USER_CREATE] ERROR: Failed to create team sheet:`, sheetError);
      return Response.json(
        { error: "Failed to create initial team" },
        { status: 500 }
      );
    }

    const duration = Date.now() - startTime;
    console.log(`[USER_CREATE] COMPLETED in ${duration}ms`);

    return Response.json(
      {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          hasOnboarded: user.hasOnboarded,
        },
      },
      {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[USER_CREATE] ERROR after ${duration}ms:`, error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
