// app/api/user/onboarding+api.ts - User onboarding API route for Expo Router
import { prisma } from "../../../lib/prisma";
import { getAuthUser } from "../../../lib/auth";

// GET - Check if user exists and get onboarding status
export async function GET() {
  try {
    const authUser = await getAuthUser();

    if (!authUser) {
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: authUser.id },
      select: {
        id: true,
        email: true,
        name: true,
        hasOnboarded: true,
        totalPoints: true,
        transfersLeft: true,
        moneyLeft: true,
        favoriteClub: true,
        phone: true,
        country: true,
        createdAt: true,
      },
    });

    if (!user) {
      return Response.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    return Response.json(user, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error("Error checking onboarding status:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT - Update user onboarding status
export async function PUT(request: Request) {
  try {
    const authUser = await getAuthUser();

    if (!authUser) {
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { hasOnboarded } = await request.json();

    const user = await prisma.user.update({
      where: { id: authUser.id },
      data: { hasOnboarded },
      select: {
        id: true,
        email: true,
        name: true,
        hasOnboarded: true,
        totalPoints: true,
        transfersLeft: true,
        moneyLeft: true,
        favoriteClub: true,
        phone: true,
        country: true,
        createdAt: true,
      },
    });

    return Response.json(user, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error("Error updating onboarding status:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
