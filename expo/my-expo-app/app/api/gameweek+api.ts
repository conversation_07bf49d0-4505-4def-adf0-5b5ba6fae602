// app/api/gameweek+api.ts - Gameweek API route for Expo Router
import { prisma } from "../../lib/prisma";
import { CacheService } from "../../lib/cache";

// Mock gameweeks data for development
const mockGameweeks = [
  {
    id: 'gw1-2025',
    GW: 1,
    season: '2025',
    isActive: true,
    deadline: new Date('2025-01-15T18:00:00Z').toISOString(),
    isFinished: false,
  },
  {
    id: 'gw2-2025',
    GW: 2,
    season: '2025',
    isActive: false,
    deadline: new Date('2025-01-22T18:00:00Z').toISOString(),
    isFinished: false,
  },
  {
    id: 'gw3-2025',
    GW: 3,
    season: '2025',
    isActive: false,
    deadline: new Date('2025-01-29T18:00:00Z').toISOString(),
    isFinished: false,
  },
];

export async function GET() {
  try {
    const gameweeks = await CacheService.getGameweeks(async () => {
      const dbGameweeks = await prisma.gameWeek.findMany({
        where: { season: "2025" },
        orderBy: { GW: "asc" },
      });

      // If no gameweeks in database, return mock data
      if (dbGameweeks.length === 0) {
        return mockGameweeks;
      }

      return dbGameweeks;
    });

    return Response.json(gameweeks, {
      status: 200,
      headers: {
        'Compression': 'gzip',
        'Cache-Control': 'no-store',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Gameweeks API Error:', error);
    return Response.json(
      { error: 'Failed to fetch gameweeks' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
