// app/api/leagues+api.ts - Leagues API route for Expo Router
import { prisma } from "../../lib/prisma";
import { getAuthUser } from "../../lib/auth";
import { CacheService } from "../../lib/cache";

// Mock leagues data for development
const mockLeagues = [
  {
    id: 'league1',
    name: 'Tunisia Fantasy League',
    code: 'TFL001',
    status: 'PRIVATE',
    type: 'STANDING',
    startFromGW: 'gw1-2025',
    admins: [
      {
        id: 'user1',
        name: '<PERSON>',
        email: '<EMAIL>',
        totalPoints: 150,
        favoriteClub: 'est',
      },
    ],
    members: [
      {
        id: 'user1',
        name: '<PERSON>',
        email: '<EMAIL>',
        totalPoints: 150,
        favoriteClub: 'est',
      },
      {
        id: 'user2',
        name: 'Fat<PERSON> Trabelsi',
        email: '<EMAIL>',
        totalPoints: 135,
        favoriteClub: 'ca',
      },
    ],
    _count: {
      members: 2,
    },
  },
  {
    id: 'league2',
    name: 'Friends League',
    code: 'FRD123',
    status: 'PRIVATE',
    type: 'STANDING',
    startFromGW: 'gw1-2025',
    admins: [
      {
        id: 'user2',
        name: 'Fatma Trabelsi',
        email: '<EMAIL>',
        totalPoints: 135,
        favoriteClub: 'ca',
      },
    ],
    members: [
      {
        id: 'user2',
        name: 'Fatma Trabelsi',
        email: '<EMAIL>',
        totalPoints: 135,
        favoriteClub: 'ca',
      },
    ],
    _count: {
      members: 1,
    },
  },
];

// Generate random code for leagues
function generateRandomCode(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// GET - Fetch user's leagues
export async function GET() {
  try {
    const authUser = await getAuthUser();
    const userId = authUser?.id;
    
    if (!userId) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const leagues = await CacheService.getLeagues(async () => {
      const dbLeagues = await prisma.league.findMany({
        where: {
          OR: [
            { admins: { some: { id: userId } } },
            { members: { some: { id: userId } } },
          ],
        },
        include: {
          admins: {
            select: {
              id: true,
              name: true,
              email: true,
              totalPoints: true,
              favoriteClub: true,
            },
          },
          members: {
            select: {
              id: true,
              name: true,
              email: true,
              totalPoints: true,
              favoriteClub: true,
            },
          },
          _count: {
            select: {
              members: true,
            },
          },
        },
      });

      // If no leagues in database, return mock data
      if (dbLeagues.length === 0) {
        return mockLeagues;
      }

      return dbLeagues;
    }, userId);

    return Response.json(leagues, {
      headers: {
        'Compression': 'gzip',
        'Cache-Control': 'no-store',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error("Error fetching leagues:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST - Create new league
export async function POST(req: Request) {
  const startTime = Date.now();
  console.log(
    `[LEAGUE_CREATE] Starting league creation request at ${new Date().toISOString()}`
  );

  try {
    const authUser = await getAuthUser();
    const userId = authUser?.id;
    
    if (!authUser || !userId) {
      console.log(`[LEAGUE_CREATE] ERROR: Unauthorized - No auth user found`);
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, status = 'PRIVATE', type = 'STANDING' } = await req.json();

    console.log(`[LEAGUE_CREATE] User ${userId} creating league: ${name}`);

    // Validation
    if (!name || typeof name !== 'string') {
      console.log(`[LEAGUE_CREATE] ERROR: Missing or invalid league name`);
      return Response.json(
        { error: "Missing required field: name" },
        { status: 400 }
      );
    }

    if (name.trim().length < 6) {
      console.log(`[LEAGUE_CREATE] ERROR: League name too short`);
      return Response.json(
        { error: "League name must be at least 6 characters" },
        { status: 400 }
      );
    }

    if (name.trim().length > 20) {
      console.log(`[LEAGUE_CREATE] ERROR: League name too long`);
      return Response.json(
        { error: "League name must be less than 20 characters" },
        { status: 400 }
      );
    }

    console.log(`[LEAGUE_CREATE] Validation passed - Creating league`);

    // Check if league with same name already exists
    const existingLeague = await prisma.league.findFirst({
      where: { name: name.trim() },
    });

    if (existingLeague) {
      console.log(
        `[LEAGUE_CREATE] ERROR: League with name "${name}" already exists`
      );
      return Response.json(
        { error: "League with this name already exists" },
        { status: 409 }
      );
    }

    // Generate unique code
    const MAX_ATTEMPTS = 100;
    let attempts = 0;
    let code = generateRandomCode(6);
    
    while (attempts < MAX_ATTEMPTS) {
      const existingCode = await prisma.league.findFirst({
        where: { code },
      });
      if (!existingCode) {
        break;
      }
      code = generateRandomCode(6);
      attempts++;
    }

    if (attempts >= MAX_ATTEMPTS) {
      console.log(`[LEAGUE_CREATE] ERROR: Failed to generate unique code`);
      return Response.json(
        { error: "Failed to generate unique code" },
        { status: 500 }
      );
    }

    console.log(`[LEAGUE_CREATE] Generated code: ${code}`);

    // Create league with the user as admin and member
    const newLeague = await prisma.league.create({
      data: {
        name: name.trim(),
        code,
        status: status as any,
        type: type as any,
      },
    });

    await prisma.league.update({
      where: { id: newLeague.id },
      data: {
        admins: {
          connect: { id: userId },
        },
        members: {
          connect: { id: userId },
        },
      },
    });

    const duration = Date.now() - startTime;
    console.log(`[LEAGUE_CREATE] SUCCESS: League created in ${duration}ms`);

    // Invalidate cache
    await CacheService.invalidateLeaguesCache();

    return Response.json(
      { success: true, league: { ...newLeague, code } },
      {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[LEAGUE_CREATE] ERROR after ${duration}ms:`, error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
