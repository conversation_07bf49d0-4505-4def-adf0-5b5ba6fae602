// app/api/leagues/join+api.ts - Join league API route for Expo Router
import { prisma } from "../../../lib/prisma";
import { getAuthUser } from "../../../lib/auth";
import { CacheService } from "../../../lib/cache";

// PUT - Join a league by code
export async function PUT(req: Request) {
  const startTime = Date.now();
  console.log(`[LEAGUE_JOIN] Starting league join request at ${new Date().toISOString()}`);

  try {
    // Check authentication
    const authUser = await getAuthUser();
    const userId = authUser?.id;
    if (!authUser || !userId) {
      console.log(`[LEAGUE_JOIN] ERROR: Unauthorized - No auth user found`);
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const code = searchParams.get("code");

    console.log(`[LEAGUE_JOIN] User ${userId} attempting to join league with code: ${code}`);

    if (!code) {
      console.log(`[LEAGUE_JOIN] ERROR: Missing league code`);
      return Response.json(
        { error: "Missing required field: code" },
        { status: 400 }
      );
    }

    // Check if league exists
    const league = await prisma.league.findUnique({
      where: { code },
      include: {
        members: true,
      },
    });

    if (!league) {
      console.log(`[LEAGUE_JOIN] ERROR: League not found with code: ${code}`);
      return Response.json(
        { error: "League not found" },
        { status: 404 }
      );
    }

    console.log(`[LEAGUE_JOIN] League found: ${league.name} (${league.id})`);

    // Check if user is already a member
    const isAlreadyMember = league.members.some(member => member.id === userId);
    if (isAlreadyMember) {
      console.log(`[LEAGUE_JOIN] ERROR: User ${userId} is already a member of league ${league.id}`);
      return Response.json(
        { error: "You are already a member of this league" },
        { status: 409 }
      );
    }

    // Check if league is private (for now, all leagues are joinable)
    if (league.status === 'PRIVATE') {
      console.log(`[LEAGUE_JOIN] Joining private league: ${league.name}`);
    }

    // Add user to league
    await prisma.league.update({
      where: { id: league.id },
      data: {
        members: {
          connect: { id: userId },
        },
      },
    });

    const duration = Date.now() - startTime;
    console.log(`[LEAGUE_JOIN] SUCCESS: User ${userId} joined league ${league.id} in ${duration}ms`);

    // Invalidate cache
    await CacheService.invalidateLeaguesCache();

    return Response.json(
      { success: true, message: "Successfully joined league" },
      {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[LEAGUE_JOIN] ERROR after ${duration}ms:`, error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
