// app/api/leagues/[id]+api.ts - Individual league management API route for Expo Router
import { prisma } from "../../../lib/prisma";
import { getAuthUser } from "../../../lib/auth";
import { CacheService } from "../../../lib/cache";

// GET - Get league details
export async function GET(
  req: Request,
  { id }: { id: string }
) {
  try {
    const authUser = await getAuthUser();
    const userId = authUser?.id;
    
    if (!userId) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const league = await prisma.league.findUnique({
      where: { id },
      include: {
        admins: {
          select: {
            id: true,
            name: true,
            email: true,
            totalPoints: true,
            favoriteClub: true,
          },
        },
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            totalPoints: true,
            favoriteClub: true,
          },
          orderBy: {
            totalPoints: 'desc',
          },
        },
        _count: {
          select: {
            members: true,
          },
        },
      },
    });

    if (!league) {
      return Response.json({ error: "League not found" }, { status: 404 });
    }

    // Check if user is a member
    const isMember = league.members.some(member => member.id === userId);
    if (!isMember) {
      return Response.json({ error: "Access denied" }, { status: 403 });
    }

    return Response.json(league, {
      headers: {
        'Compression': 'gzip',
        'Cache-Control': 'no-store',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error("Error fetching league:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT - Update league (admin only)
export async function PUT(
  req: Request,
  { id }: { id: string }
) {
  const startTime = Date.now();
  console.log(
    `[LEAGUE_UPDATE] Starting league update request for league ${id} at ${new Date().toISOString()}`
  );

  try {
    // Check authentication
    const authUser = await getAuthUser();
    const userId = authUser?.id;
    if (!authUser || !userId) {
      console.log(`[LEAGUE_UPDATE] ERROR: Unauthorized - No auth user found`);
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`[LEAGUE_UPDATE] Authentication successful for user: ${userId}`);

    // Check if user is admin of this league
    const league = await prisma.league.findUnique({
      where: { id },
      include: {
        admins: true,
      },
    });

    if (!league) {
      return Response.json({ error: "League not found" }, { status: 404 });
    }

    if (league.status !== 'PRIVATE') {
      return Response.json(
        { error: "Cannot update public league" },
        { status: 403 }
      );
    }

    const isAdmin = league.admins.some((admin) => admin.id === userId);
    if (!isAdmin) {
      console.log(
        `[LEAGUE_UPDATE] ERROR: User ${userId} is not admin of league ${id}`
      );
      return Response.json({ error: "Access denied" }, { status: 403 });
    }

    const { name } = await req.json();

    console.log(`[LEAGUE_UPDATE] Admin ${userId} updating league ${id}: ${name}`);

    // Validation
    if (!name || typeof name !== 'string') {
      console.log(`[LEAGUE_UPDATE] ERROR: Missing or invalid league name`);
      return Response.json(
        { error: "Missing required field: name" },
        { status: 400 }
      );
    }

    if (name.trim().length < 6) {
      console.log(`[LEAGUE_UPDATE] ERROR: League name too short`);
      return Response.json(
        { error: "League name must be at least 6 characters" },
        { status: 400 }
      );
    }

    if (name.trim().length > 20) {
      console.log(`[LEAGUE_UPDATE] ERROR: League name too long`);
      return Response.json(
        { error: "League name must be less than 20 characters" },
        { status: 400 }
      );
    }

    // Check if another league with same name exists
    const existingLeague = await prisma.league.findFirst({
      where: {
        name: name.trim(),
        id: { not: id },
      },
    });

    if (existingLeague) {
      console.log(
        `[LEAGUE_UPDATE] ERROR: League with name "${name}" already exists`
      );
      return Response.json(
        { error: "League with this name already exists" },
        { status: 409 }
      );
    }

    console.log(`[LEAGUE_UPDATE] Validation passed - Updating league`);

    // Update league
    const updatedLeague = await prisma.league.update({
      where: { id },
      data: {
        name: name.trim(),
      },
      include: {
        admins: {
          select: {
            id: true,
            name: true,
            email: true,
            totalPoints: true,
            favoriteClub: true,
          },
        },
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            totalPoints: true,
            favoriteClub: true,
          },
          orderBy: {
            totalPoints: 'desc',
          },
        },
        _count: {
          select: {
            members: true,
          },
        },
      },
    });

    const duration = Date.now() - startTime;
    console.log(`[LEAGUE_UPDATE] SUCCESS: League updated in ${duration}ms`);

    // Invalidate cache
    await CacheService.invalidateLeaguesCache();

    return Response.json(updatedLeague, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[LEAGUE_UPDATE] ERROR after ${duration}ms:`, error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE - Leave league
export async function DELETE(
  req: Request,
  { id }: { id: string }
) {
  const startTime = Date.now();
  console.log(
    `[LEAGUE_LEAVE] Starting league leave request for league ${id} at ${new Date().toISOString()}`
  );

  try {
    // Check authentication
    const authUser = await getAuthUser();
    const userId = authUser?.id;
    if (!authUser || !userId) {
      console.log(`[LEAGUE_LEAVE] ERROR: Unauthorized - No auth user found`);
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`[LEAGUE_LEAVE] User ${userId} leaving league ${id}`);

    // Check if league exists and user is a member
    const league = await prisma.league.findUnique({
      where: { id },
      include: {
        admins: true,
        members: true,
      },
    });

    if (!league) {
      return Response.json({ error: "League not found" }, { status: 404 });
    }

    const isMember = league.members.some(member => member.id === userId);
    if (!isMember) {
      console.log(`[LEAGUE_LEAVE] ERROR: User ${userId} is not a member of league ${id}`);
      return Response.json({ error: "You are not a member of this league" }, { status: 400 });
    }

    const isAdmin = league.admins.some(admin => admin.id === userId);
    const isOnlyAdmin = isAdmin && league.admins.length === 1;

    // If user is the only admin and there are other members, prevent leaving
    if (isOnlyAdmin && league.members.length > 1) {
      console.log(`[LEAGUE_LEAVE] ERROR: User ${userId} is the only admin and cannot leave`);
      return Response.json(
        { error: "Cannot leave league as the only admin. Transfer admin rights first." },
        { status: 400 }
      );
    }

    // Remove user from league
    await prisma.league.update({
      where: { id },
      data: {
        members: {
          disconnect: { id: userId },
        },
        admins: {
          disconnect: { id: userId },
        },
      },
    });

    // If this was the last member, delete the league
    if (league.members.length === 1) {
      await prisma.league.delete({
        where: { id },
      });
      console.log(`[LEAGUE_LEAVE] League ${id} deleted as it had no remaining members`);
    }

    const duration = Date.now() - startTime;
    console.log(`[LEAGUE_LEAVE] SUCCESS: User ${userId} left league ${id} in ${duration}ms`);

    // Invalidate cache
    await CacheService.invalidateLeaguesCache();

    return Response.json(
      { success: true, message: "Successfully left league" },
      {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[LEAGUE_LEAVE] ERROR after ${duration}ms:`, error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
