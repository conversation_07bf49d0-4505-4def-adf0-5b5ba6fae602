// app/api/fixtures+api.ts - Fixtures API route for Expo Router
import { prisma } from "../../lib/prisma";
import { CacheService } from "../../lib/cache";

// Mock fixtures data for development
const mockFixtures = [
  {
    id: 'fixture1',
    GW: 'gw1-2025',
    teamHId: 'est',
    teamAId: 'ca',
    kickoffTime: new Date('2025-01-15T15:00:00Z').toISOString(),
    teamHScore: null,
    teamAScore: null,
    status: 'SCHEDULED',
    teamH: {
      id: 'est',
      name: 'Espérance Sportive de Tunis',
      abbr: 'EST',
      logo: '/logos/est.webp',
    },
    teamA: {
      id: 'ca',
      name: 'Club Africain',
      abbr: 'CA',
      logo: '/logos/ca.webp',
    },
    teamHPlayers: [],
    teamAPlayers: [],
  },
  {
    id: 'fixture2',
    GW: 'gw1-2025',
    teamHId: 'css',
    teamAId: 'st',
    kickoffTime: new Date('2025-01-15T17:00:00Z').toISOString(),
    teamHScore: null,
    teamAScore: null,
    status: 'SCHEDULED',
    teamH: {
      id: 'css',
      name: 'Club Sportif Sfaxien',
      abbr: 'CSS',
      logo: '/logos/css.webp',
    },
    teamA: {
      id: 'st',
      name: 'Stade Tunisien',
      abbr: 'ST',
      logo: '/logos/st.webp',
    },
    teamHPlayers: [],
    teamAPlayers: [],
  },
];

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const round = searchParams.get("round");
    const GW = searchParams.get("GW");
    const teamId = searchParams.get("teamId");

    const whereClause: Record<string, unknown> = {};

    if (round) {
      // If round is provided, find gameweek by round number
      const gameweek = await prisma.gameWeek.findFirst({
        where: { GW: parseInt(round) },
      });
      if (gameweek) {
        whereClause.GW = gameweek.id;
      }
    } else if (GW) {
      whereClause.GW = GW;
    }

    if (teamId) {
      whereClause.OR = [{ teamHId: teamId }, { teamAId: teamId }];
    }

    const fixtures = await CacheService.getFixtures(async () => {
      const dbFixtures = await prisma.fixture.findMany({
        where: whereClause,
        include: {
          teamH: {
            select: {
              id: true,
              name: true,
              abbr: true,
              logo: true,
            },
          },
          teamA: {
            select: {
              id: true,
              name: true,
              abbr: true,
              logo: true,
            },
          },
        },
        orderBy: [{ GW: "asc" }, { kickoffTime: "asc" }],
      });

      // If no fixtures in database, return mock data
      if (dbFixtures.length === 0) {
        return mockFixtures;
      }

      // Get player stats for each fixture
      const PlayerStats = await prisma.player.findMany({
        where: {
          teamId: {
            in: [...new Set([...dbFixtures.map((f) => f.teamHId), ...dbFixtures.map((f) => f.teamAId)])],
          },
        },
        include: {
          gwStats: {
            where: {
              GW: {
                in: dbFixtures.map((f) => f.GW),
              },
            },
          },
        },
      });

      const fixturesWithStats = dbFixtures.map((fixture) => {
        const teamHPlayers = PlayerStats.filter(
          (p) => p.teamId === fixture.teamHId
        );
        const teamAPlayers = PlayerStats.filter(
          (p) => p.teamId === fixture.teamAId
        );
        return {
          ...fixture,
          teamHPlayers,
          teamAPlayers,
        };
      });

      return fixturesWithStats;
    });

    return Response.json(fixtures, {
      status: 200,
      headers: {
        'Compression': 'gzip',
        'Cache-Control': 'no-store',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error("Error fetching fixtures:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
