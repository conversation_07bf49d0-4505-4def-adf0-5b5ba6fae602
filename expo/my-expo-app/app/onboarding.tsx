// app/onboarding.tsx - Onboarding page
import { useAuth } from '../contexts/AuthContext';
import { Redirect } from 'expo-router';
import OnboardingScreen from '../screens/OnboardingScreen';

export default function OnboardingPage() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return null; // Or loading component
  }

  if (!user) {
    return <Redirect href="/login" />;
  }

  // If user is already onboarded, redirect to home
  if (user.user_metadata?.hasOnboarded) {
    return <Redirect href="/" />;
  }

  return <OnboardingScreen />;
}
