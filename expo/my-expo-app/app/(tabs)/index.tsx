// app/(tabs)/index.tsx - Home tab
import { useAuth } from '../../contexts/AuthContext';
import { Redirect } from 'expo-router';
import HomeScreen from '../../screens/HomeScreen';

export default function HomeTab() {
  const { user, loading, checkOnboardingStatus } = useAuth();

  if (loading) {
    return null; // Or loading component
  }

  if (!user) {
    return <Redirect href="/login" />;
  }

  // Check if user needs onboarding
  if (!checkOnboardingStatus()) {
    return <Redirect href="/onboarding" />;
  }

  return <HomeScreen />;
}
