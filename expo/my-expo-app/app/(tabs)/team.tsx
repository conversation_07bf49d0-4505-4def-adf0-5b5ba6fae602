// app/(tabs)/team.tsx - Team tab
import { useAuth } from '../../contexts/AuthContext';
import { Redirect } from 'expo-router';
import TeamScreen from '../../screens/TeamScreen';

export default function TeamTab() {
  const { user, loading, checkOnboardingStatus } = useAuth();

  if (loading) {
    return null; // Or loading component
  }

  if (!user) {
    return <Redirect href="/login" />;
  }

  // Check if user needs onboarding
  if (!checkOnboardingStatus()) {
    return <Redirect href="/onboarding" />;
  }

  return <TeamScreen />;
}
