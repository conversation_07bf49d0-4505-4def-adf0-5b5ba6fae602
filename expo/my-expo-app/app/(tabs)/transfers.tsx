// app/(tabs)/transfers.tsx - Transfers tab
import { useAuth } from '../../contexts/AuthContext';
import { Redirect } from 'expo-router';
import TransferScreen from '../../screens/TransferScreen';

export default function TransfersTab() {
  const { user, loading, checkOnboardingStatus } = useAuth();

  if (loading) {
    return null; // Or loading component
  }

  if (!user) {
    return <Redirect href="/login" />;
  }

  // Check if user needs onboarding
  if (!checkOnboardingStatus()) {
    return <Redirect href="/onboarding" />;
  }

  return <TransferScreen />;
}
