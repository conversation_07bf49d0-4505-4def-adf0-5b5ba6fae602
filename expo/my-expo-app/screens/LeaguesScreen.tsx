// screens/LeaguesScreen.tsx - Main leagues screen for mobile
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  RefreshControl,
  Clipboard,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useLeagueStore, League } from '../store/useLeagueStore';
import { useRouter } from 'expo-router';
import Header from '../components/Header';
import Modal from '../components/Modal';

export default function LeaguesScreen() {
  const { user } = useAuth();
  const router = useRouter();
  const [refreshing, setRefreshing] = useState(false);

  const {
    leagues,
    loading,
    error,
    searchTerm,
    createDialogOpen,
    joinDialogOpen,
    newLeagueName,
    joinCode,
    copiedLeagueId,
    setSearchTerm,
    setCreateDialogOpen,
    setJoinDialogOpen,
    setNewLeagueName,
    setJoinCode,
    setCopiedLeagueId,
    fetchLeagues,
    createLeague,
    joinLeague,
    leaveLeague,
  } = useLeagueStore();

  // Initialize leagues
  useEffect(() => {
    fetchLeagues();
  }, []);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchLeagues();
    setRefreshing(false);
  };

  // Filter leagues based on search term
  const filteredLeagues = leagues.filter(league =>
    league.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Check if user is admin of a league
  const isUserAdmin = (league: League) => {
    return league.admins.some(admin => admin.id === user?.id);
  };

  // Check if user is member of a league
  const isUserMember = (league: League) => {
    return league.members.some(member => member.id === user?.id);
  };

  // Handle create league
  const handleCreateLeague = async () => {
    if (!newLeagueName.trim()) {
      Alert.alert('Error', 'Please enter a league name');
      return;
    }

    if (newLeagueName.trim().length < 6) {
      Alert.alert('Error', 'League name must be at least 6 characters');
      return;
    }

    if (newLeagueName.trim().length > 20) {
      Alert.alert('Error', 'League name must be less than 20 characters');
      return;
    }

    const success = await createLeague(newLeagueName.trim());
    if (success) {
      Alert.alert('Success', 'League created successfully!');
      setCreateDialogOpen(false);
      setNewLeagueName('');
    } else if (error) {
      Alert.alert('Error', error);
    }
  };

  // Handle join league
  const handleJoinLeague = async () => {
    if (!joinCode.trim()) {
      Alert.alert('Error', 'Please enter a league code');
      return;
    }

    const success = await joinLeague(joinCode.trim());
    if (success) {
      Alert.alert('Success', 'Joined league successfully!');
      setJoinDialogOpen(false);
      setJoinCode('');
    } else if (error) {
      Alert.alert('Error', error);
    }
  };

  // Handle leave league
  const handleLeaveLeague = (league: League) => {
    Alert.alert(
      'Leave League',
      `Are you sure you want to leave "${league.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: async () => {
            const success = await leaveLeague(league.id);
            if (success) {
              Alert.alert('Success', 'Left league successfully!');
            } else if (error) {
              Alert.alert('Error', error);
            }
          },
        },
      ]
    );
  };

  // Copy league code
  const copyLeagueCode = (league: League) => {
    Clipboard.setString(league.code);
    setCopiedLeagueId(league.id);
    Alert.alert('Copied', 'League code copied to clipboard!');
    
    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopiedLeagueId(null);
    }, 2000);
  };

  // Navigate to league details
  const navigateToLeague = (league: League) => {
    router.push(`/leagues/${league.id}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header title="Leagues" />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header Section */}
        <View style={styles.headerSection}>
          <Text style={styles.title}>🏆 My Leagues</Text>
          <Text style={styles.description}>
            Create or join leagues to compete with friends
          </Text>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search leagues..."
            placeholderTextColor="#64748b"
            value={searchTerm}
            onChangeText={setSearchTerm}
          />
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.createButton}
            onPress={() => setCreateDialogOpen(true)}
          >
            <Text style={styles.createButtonText}>+ Create League</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.joinButton}
            onPress={() => setJoinDialogOpen(true)}
          >
            <Text style={styles.joinButtonText}>Join League</Text>
          </TouchableOpacity>
        </View>

        {/* Error Message */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {/* Loading State */}
        {loading && leagues.length === 0 && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading leagues...</Text>
          </View>
        )}

        {/* Empty State */}
        {!loading && filteredLeagues.length === 0 && !error && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyTitle}>No leagues found</Text>
            <Text style={styles.emptyDescription}>
              {searchTerm ? 'Try a different search term' : 'Create your first league or join one with a code'}
            </Text>
          </View>
        )}

        {/* Leagues List */}
        <View style={styles.leaguesContainer}>
          {filteredLeagues.map((league) => (
            <TouchableOpacity
              key={league.id}
              style={styles.leagueCard}
              onPress={() => navigateToLeague(league)}
            >
              <View style={styles.leagueHeader}>
                <View style={styles.leagueInfo}>
                  <Text style={styles.leagueName}>{league.name}</Text>
                  <Text style={styles.leagueCode}>Code: {league.code}</Text>
                </View>
                <View style={styles.leagueStats}>
                  <Text style={styles.memberCount}>{league._count.members} members</Text>
                  {isUserAdmin(league) && (
                    <View style={styles.adminBadge}>
                      <Text style={styles.adminBadgeText}>Admin</Text>
                    </View>
                  )}
                </View>
              </View>

              <View style={styles.leagueActions}>
                <TouchableOpacity
                  style={styles.copyButton}
                  onPress={() => copyLeagueCode(league)}
                >
                  <Text style={styles.copyButtonText}>
                    {copiedLeagueId === league.id ? 'Copied!' : 'Copy Code'}
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.leaveButton}
                  onPress={() => handleLeaveLeague(league)}
                >
                  <Text style={styles.leaveButtonText}>Leave</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Create League Modal */}
      <Modal
        visible={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        title="Create League"
      >
        <View style={styles.modalContent}>
          <Text style={styles.modalDescription}>
            Enter a name for your new league
          </Text>
          
          <TextInput
            style={styles.modalInput}
            placeholder="League name (6-20 characters)"
            placeholderTextColor="#64748b"
            value={newLeagueName}
            onChangeText={setNewLeagueName}
            maxLength={20}
          />
          
          <View style={styles.modalActions}>
            <TouchableOpacity
              style={styles.modalCancelButton}
              onPress={() => setCreateDialogOpen(false)}
            >
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.modalCreateButton}
              onPress={handleCreateLeague}
              disabled={loading}
            >
              <Text style={styles.modalCreateText}>
                {loading ? 'Creating...' : 'Create'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Join League Modal */}
      <Modal
        visible={joinDialogOpen}
        onClose={() => setJoinDialogOpen(false)}
        title="Join League"
      >
        <View style={styles.modalContent}>
          <Text style={styles.modalDescription}>
            Enter the league code to join
          </Text>
          
          <TextInput
            style={styles.modalInput}
            placeholder="League code"
            placeholderTextColor="#64748b"
            value={joinCode}
            onChangeText={setJoinCode}
            autoCapitalize="characters"
          />
          
          <View style={styles.modalActions}>
            <TouchableOpacity
              style={styles.modalCancelButton}
              onPress={() => setJoinDialogOpen(false)}
            >
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.modalJoinButton}
              onPress={handleJoinLeague}
              disabled={loading}
            >
              <Text style={styles.modalJoinText}>
                {loading ? 'Joining...' : 'Join'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f172a',
  },
  scrollView: {
    flex: 1,
  },
  headerSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: '#64748b',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchInput: {
    backgroundColor: '#1e293b',
    borderWidth: 1,
    borderColor: '#334155',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#ffffff',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  createButton: {
    flex: 1,
    backgroundColor: '#3b82f6',
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  createButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  joinButton: {
    flex: 1,
    backgroundColor: '#10b981',
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  joinButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    marginHorizontal: 20,
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#dc2626',
    borderRadius: 8,
  },
  errorText: {
    color: '#ffffff',
    fontSize: 14,
    textAlign: 'center',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    color: '#64748b',
    fontSize: 16,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
  },
  leaguesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  leagueCard: {
    backgroundColor: '#1e293b',
    borderWidth: 1,
    borderColor: '#334155',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  leagueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  leagueInfo: {
    flex: 1,
  },
  leagueName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  leagueCode: {
    fontSize: 14,
    color: '#64748b',
  },
  leagueStats: {
    alignItems: 'flex-end',
  },
  memberCount: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 4,
  },
  adminBadge: {
    backgroundColor: '#dc2626',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  adminBadgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  leagueActions: {
    flexDirection: 'row',
    gap: 8,
  },
  copyButton: {
    flex: 1,
    backgroundColor: '#374151',
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  copyButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  leaveButton: {
    flex: 1,
    backgroundColor: '#dc2626',
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  leaveButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  modalContent: {
    padding: 20,
  },
  modalDescription: {
    fontSize: 16,
    color: '#64748b',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalInput: {
    backgroundColor: '#1e293b',
    borderWidth: 1,
    borderColor: '#334155',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#ffffff',
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: '#374151',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalCancelText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
  modalCreateButton: {
    flex: 1,
    backgroundColor: '#3b82f6',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalCreateText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  modalJoinButton: {
    flex: 1,
    backgroundColor: '#10b981',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalJoinText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});
