// screens/OnboardingScreen.tsx - Complete onboarding flow for mobile
import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  Dimensions,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useFantasyStore, Player } from '../store/useFantasyStore';
import { Redirect, useRouter } from 'expo-router';
import Header from '../components/Header';
import PitchView from '../components/PitchView';
import TeamTable from '../components/TeamTable';
import TeamViewToggle from '../components/TeamViewToggle';
import PlayerModal from '../components/PlayerModal';
import TransferFilters from '../components/TransferFilters';

const { width: screenWidth } = Dimensions.get('window');

// Mock data for countries and states
const countries = [
  { name: 'Tunisia', code: 'TN' },
  { name: 'Algeria', code: 'DZ' },
  { name: 'Morocco', code: 'MA' },
  { name: 'Egypt', code: 'EG' },
];

const tunisiaStates = [
  'Tunis', 'Ariana', 'Ben Arous', 'Manouba', 'Nabeul', 'Zaghouan',
  'Bizerte', 'Béja', 'Jendouba', 'Kef', 'Siliana', 'Sousse',
  'Monastir', 'Mahdia', 'Sfax', 'Kairouan', 'Kasserine', 'Sidi Bouzid',
  'Gabès', 'Medenine', 'Tataouine', 'Gafsa', 'Tozeur', 'Kebili',
];

// Generate fake team for onboarding
const generateFakeOnboardingTeam = (players: Player[]) => {
  if (players.length < 15) return { starters: [], subs: [] };

  const gks = players.filter(p => p.position === 'GK');
  const defs = players.filter(p => p.position === 'DEF');
  const mids = players.filter(p => p.position === 'MID');
  const atts = players.filter(p => p.position === 'ATK');

  const starters = [
    gks[0], // 1 GK
    ...defs.slice(0, 4), // 4 DEF
    ...mids.slice(0, 4), // 4 MID
    ...atts.slice(0, 2), // 2 ATK
  ].filter(Boolean);

  const subs = [
    gks[1], // 1 GK sub
    defs[4], // 1 DEF sub
    mids[4], // 1 MID sub
    atts[2], // 1 ATT sub
  ].filter(Boolean);

  return { starters, subs };
};

export default function OnboardingScreen() {
  const { user, updateUser } = useAuth();
  const router = useRouter();
  const isPhoneVerificationActive = false; // Disable phone verification for mobile
  
  // Current step state
  const [currentStep, setCurrentStep] = useState(isPhoneVerificationActive ? 1 : 3);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [phone, setPhone] = useState('');
  const [country, setCountry] = useState('Tunisia');
  const [state, setState] = useState('');
  const [favoriteClub, setFavoriteClub] = useState('');
  const [fullName, setFullName] = useState(user?.name || '');

  // SMS verification state (disabled for mobile)
  const [verificationCode, setVerificationCode] = useState('');
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);

  // Player selection state
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [view, setView] = useState<'pitch' | 'list'>('pitch');

  // Fantasy store
  const {
    activeGameweek,
    players,
    teams,
    isLoading,
    fetchGameweeks,
    fetchPlayers,
    fetchTeams,
  } = useFantasyStore();

  // Team state
  const [starters, setStarters] = useState<Player[]>([]);
  const [subs, setSubs] = useState<Player[]>([]);
  const [captainId, setCaptainId] = useState<string>('');
  const [viceCaptainId, setViceCaptainId] = useState<string>('');
  const [removedPlayers, setRemovedPlayers] = useState<Player[]>([]);

  // Initialize data
  useEffect(() => {
    const initializeData = async () => {
      try {
        await Promise.all([
          fetchGameweeks(),
          fetchPlayers(),
          fetchTeams(),
        ]);
      } catch (error) {
        console.error('Error initializing onboarding data:', error);
      }
    };

    initializeData();
  }, []);

  // Generate initial team when players are loaded
  useEffect(() => {
    if (players.length > 0 && starters.length === 0) {
      const { starters: initialStarters, subs: initialSubs } = generateFakeOnboardingTeam(players);
      setStarters(initialStarters);
      setSubs(initialSubs);
      
      // Set initial captain and vice-captain
      if (initialStarters.length > 0) {
        setCaptainId(initialStarters[0]?.id || '');
        setViceCaptainId(initialStarters[1]?.id || '');
      }
    }
  }, [players]);

  // Calculate budget
  const budget = useMemo(() => {
    const totalCost = [...starters, ...subs].reduce((sum, player) => sum + player.currentPrice, 0);
    return 100 - totalCost;
  }, [starters, subs]);

  // Handle player removal
  const handleRemove = (player: Player) => {
    if (starters.some(p => p.id === player.id)) {
      setStarters(prev => prev.filter(p => p.id !== player.id));
    } else if (subs.some(p => p.id === player.id)) {
      setSubs(prev => prev.filter(p => p.id !== player.id));
    }
    setRemovedPlayers(prev => [...prev, player]);
  };

  // Handle player addition
  const handleAdd = (player: Player) => {
    const removedPlayer = removedPlayers.find(p => p.position === player.position);
    if (!removedPlayer) return false;

    // Remove from removed players
    setRemovedPlayers(prev => prev.filter(p => p.id !== removedPlayer.id));

    // Add to appropriate team section
    if (starters.some(p => p.id === removedPlayer.id)) {
      setStarters(prev => [...prev, player]);
    } else {
      setSubs(prev => [...prev, player]);
    }

    return true;
  };

  // Handle captain selection
  const handleCaptainChange = (playerId: string) => {
    if (captainId === playerId) return;
    
    // If new captain was vice-captain, swap roles
    if (viceCaptainId === playerId) {
      setViceCaptainId(captainId);
    }
    setCaptainId(playerId);
  };

  // Handle vice-captain selection
  const handleViceCaptainChange = (playerId: string) => {
    if (viceCaptainId === playerId) return;
    
    // If new vice-captain was captain, swap roles
    if (captainId === playerId) {
      setCaptainId(viceCaptainId);
    }
    setViceCaptainId(playerId);
  };

  // Handle team submission
  const handleSubmit = async () => {
    if (isSubmitting) return;

    // Validation
    if (currentStep === 5) {
      if (!fullName.trim()) {
        Alert.alert('Error', 'Please enter your team name');
        return;
      }
    }

    if (currentStep === 3 || currentStep === 4) {
      if (starters.length !== 11 || subs.length !== 4) {
        Alert.alert('Error', 'Please complete your team selection (11 starters + 4 subs)');
        return;
      }
      if (!captainId || !viceCaptainId) {
        Alert.alert('Error', 'Please select captain and vice-captain');
        return;
      }
    }

    if (currentStep < 5) {
      setCurrentStep(prev => prev + 1);
      return;
    }

    // Final submission
    setIsSubmitting(true);
    try {
      const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8082';
      const response = await fetch(`${API_BASE_URL}/api/user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user?.id,
          email: user?.email,
          name: fullName,
          phone: isPhoneVerificationActive ? phone : '',
          country,
          state,
          favoriteClub,
          firstGameWeek: activeGameweek?.id,
          isPhoneVerified: isPhoneVerificationActive ? isPhoneVerified : false,
          hasOnboarded: true,
          starters: starters.map(p => p.id),
          subs: subs.map(p => p.id),
          captainId,
          viceCaptainId,
          moneyLeft: budget,
          transfersLeft: 1,
          GW_number: activeGameweek?.GW,
        }),
      });

      if (response.ok) {
        // Update user metadata
        await updateUser({ hasOnboarded: true });
        Alert.alert('Success', 'Welcome to Fantasy TPL!', [
          { text: 'OK', onPress: () => router.replace('/') }
        ]);
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to complete onboarding');
      }
    } catch (error) {
      console.error('Onboarding submission error:', error);
      Alert.alert('Error', 'Failed to complete onboarding. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle back button
  const handleBack = () => {
    if (currentStep > (isPhoneVerificationActive ? 1 : 3)) {
      setCurrentStep(prev => prev - 1);
    }
  };

  if (isLoading && players.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <Header title="Welcome" />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header title="Welcome to Fantasy TPL" />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Progress Steps */}
        <View style={styles.progressContainer}>
          <View style={styles.progressSteps}>
            {(isPhoneVerificationActive ? [1, 2, 3, 4, 5] : [3, 4, 5]).map((step) => (
              <View key={step} style={styles.stepContainer}>
                <View
                  style={[
                    styles.stepCircle,
                    currentStep === step && styles.stepCircleActive,
                    currentStep > step && styles.stepCircleCompleted,
                  ]}
                >
                  {currentStep > step ? (
                    <Text style={styles.stepCheckmark}>✓</Text>
                  ) : (
                    <Text style={[
                      styles.stepNumber,
                      currentStep === step && styles.stepNumberActive,
                    ]}>
                      {step - 2}
                    </Text>
                  )}
                </View>
                {step < (isPhoneVerificationActive ? 5 : 5) && (
                  <View style={[
                    styles.stepLine,
                    currentStep > step && styles.stepLineCompleted,
                  ]} />
                )}
              </View>
            ))}
          </View>
        </View>

        {/* Step Content */}
        <View style={styles.contentContainer}>
          {renderStepContent()}
        </View>

        {/* Navigation Buttons */}
        <View style={styles.navigationContainer}>
          {currentStep > (isPhoneVerificationActive ? 1 : 3) && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBack}
            >
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              isSubmitting && styles.nextButtonDisabled,
            ]}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <Text style={styles.nextButtonText}>
              {isSubmitting ? 'Submitting...' : currentStep === 5 ? 'Complete' : 'Next →'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Player Modal */}
      {selectedPlayer && (
        <PlayerModal
          player={selectedPlayer}
          isOpen={!!selectedPlayer}
          onClose={() => setSelectedPlayer(null)}
          onRemove={handleRemove}
          onAdd={handleAdd}
          onSelectReplacement={() => {}}
          isRemoved={removedPlayers.some(p => p.id === selectedPlayer.id)}
          canAdd={removedPlayers.length > 0}
        />
      )}
    </SafeAreaView>
  );

  function renderStepContent() {
    switch (currentStep) {
      case 1:
        return renderContactInformation();
      case 2:
        return renderPhoneVerification();
      case 3:
        return renderTeamSelection();
      case 4:
        return renderCaptainSelection();
      case 5:
        return renderTeamPreferences();
      default:
        return null;
    }
  }

  function renderContactInformation() {
    return (
      <View style={styles.stepContent}>
        <Text style={styles.stepTitle}>Contact Information</Text>
        <Text style={styles.stepDescription}>
          Add your phone number to receive important updates
        </Text>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Phone Number</Text>
          <TextInput
            style={styles.textInput}
            placeholder="+216 XX XXX XXX"
            placeholderTextColor="#64748b"
            value={phone}
            onChangeText={setPhone}
            keyboardType="phone-pad"
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Country</Text>
          <View style={styles.selectContainer}>
            <Text style={styles.selectText}>{country}</Text>
          </View>
        </View>

        {country === 'Tunisia' && (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>State</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Select state"
              placeholderTextColor="#64748b"
              value={state}
              onChangeText={setState}
            />
          </View>
        )}
      </View>
    );
  }

  function renderPhoneVerification() {
    return (
      <View style={styles.stepContent}>
        <Text style={styles.stepTitle}>Verify Your Number</Text>
        <Text style={styles.stepDescription}>
          Enter the verification code sent to {phone}
        </Text>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Verification Code</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter 6-digit code"
            placeholderTextColor="#64748b"
            value={verificationCode}
            onChangeText={setVerificationCode}
            keyboardType="number-pad"
            maxLength={6}
          />
        </View>
      </View>
    );
  }

  function renderTeamSelection() {
    return (
      <View style={styles.stepContent}>
        <Text style={styles.stepTitle}>Choose Your Team</Text>
        <Text style={styles.stepDescription}>
          Build your squad with 11 starters and 4 substitutes
        </Text>

        {/* Budget Info */}
        <View style={styles.budgetContainer}>
          <View style={styles.budgetItem}>
            <Text style={styles.budgetIcon}>👥</Text>
            <Text style={styles.budgetValue}>{starters.length + subs.length}</Text>
            <Text style={styles.budgetLabel}>Players</Text>
          </View>
          <View style={styles.budgetItem}>
            <Text style={styles.budgetIcon}>💰</Text>
            <Text style={styles.budgetValue}>{budget.toFixed(1)}M</Text>
            <Text style={styles.budgetLabel}>Budget</Text>
          </View>
        </View>

        {/* View Toggle */}
        <View style={styles.viewToggleContainer}>
          <TeamViewToggle view={view} setView={setView} />
        </View>

        {/* Team Display */}
        <View style={styles.teamContainer}>
          {view === 'pitch' ? (
            <PitchView
              starters={starters}
              subs={subs}
              onSelect={setSelectedPlayer}
              showPrice={true}
              showRole={false}
            />
          ) : (
            <View>
              <Text style={styles.sectionTitle}>Starters</Text>
              <TeamTable
                players={starters}
                onSelect={setSelectedPlayer}
                showPoints={false}
              />
              <Text style={styles.sectionTitle}>Substitutes</Text>
              <TeamTable
                players={subs}
                onSelect={setSelectedPlayer}
                showPoints={false}
              />
            </View>
          )}
        </View>
      </View>
    );
  }

  function renderCaptainSelection() {
    return (
      <View style={styles.stepContent}>
        <Text style={styles.stepTitle}>Choose Roles</Text>
        <Text style={styles.stepDescription}>
          Select your captain and vice-captain from your starters
        </Text>

        {/* Captain Selection */}
        <View style={styles.roleContainer}>
          <Text style={styles.roleTitle}>Captain (2x points)</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.playerSelectionRow}>
              {starters.map((player) => (
                <TouchableOpacity
                  key={player.id}
                  style={[
                    styles.playerSelectionCard,
                    captainId === player.id && styles.playerSelectionCardActive,
                  ]}
                  onPress={() => handleCaptainChange(player.id)}
                >
                  <Text style={styles.playerSelectionName}>{player.name}</Text>
                  <Text style={styles.playerSelectionPosition}>{player.position}</Text>
                  {captainId === player.id && (
                    <Text style={styles.playerSelectionBadge}>C</Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Vice-Captain Selection */}
        <View style={styles.roleContainer}>
          <Text style={styles.roleTitle}>Vice-Captain</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.playerSelectionRow}>
              {starters.filter(p => p.id !== captainId).map((player) => (
                <TouchableOpacity
                  key={player.id}
                  style={[
                    styles.playerSelectionCard,
                    viceCaptainId === player.id && styles.playerSelectionCardActive,
                  ]}
                  onPress={() => handleViceCaptainChange(player.id)}
                >
                  <Text style={styles.playerSelectionName}>{player.name}</Text>
                  <Text style={styles.playerSelectionPosition}>{player.position}</Text>
                  {viceCaptainId === player.id && (
                    <Text style={styles.playerSelectionBadge}>VC</Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      </View>
    );
  }

  function renderTeamPreferences() {
    return (
      <View style={styles.stepContent}>
        <Text style={styles.stepTitle}>Team Preferences</Text>
        <Text style={styles.stepDescription}>
          Choose your team name and favorite club
        </Text>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Team Name</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter your team name"
            placeholderTextColor="#64748b"
            value={fullName}
            onChangeText={setFullName}
            maxLength={25}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Favorite Club (Optional)</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Select your favorite club"
            placeholderTextColor="#64748b"
            value={favoriteClub}
            onChangeText={setFavoriteClub}
          />
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f172a',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    color: '#64748b',
    fontSize: 16,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  progressSteps: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#374151',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#4b5563',
  },
  stepCircleActive: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  stepCircleCompleted: {
    backgroundColor: '#10b981',
    borderColor: '#10b981',
  },
  stepNumber: {
    color: '#9ca3af',
    fontSize: 14,
    fontWeight: '600',
  },
  stepNumberActive: {
    color: '#ffffff',
  },
  stepCheckmark: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  stepLine: {
    width: 24,
    height: 2,
    backgroundColor: '#4b5563',
    marginHorizontal: 8,
  },
  stepLineCompleted: {
    backgroundColor: '#10b981',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    marginBottom: 24,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#1e293b',
    borderWidth: 1,
    borderColor: '#334155',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#ffffff',
  },
  selectContainer: {
    backgroundColor: '#1e293b',
    borderWidth: 1,
    borderColor: '#334155',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectText: {
    fontSize: 16,
    color: '#ffffff',
  },
  budgetContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#1e293b',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#334155',
  },
  budgetItem: {
    alignItems: 'center',
  },
  budgetIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  budgetValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  budgetLabel: {
    fontSize: 12,
    color: '#64748b',
  },
  viewToggleContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  teamContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 12,
    marginTop: 16,
  },
  roleContainer: {
    marginBottom: 24,
  },
  roleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 12,
  },
  playerSelectionRow: {
    flexDirection: 'row',
    gap: 12,
  },
  playerSelectionCard: {
    backgroundColor: '#1e293b',
    borderWidth: 1,
    borderColor: '#334155',
    borderRadius: 12,
    padding: 12,
    minWidth: 120,
    alignItems: 'center',
    position: 'relative',
  },
  playerSelectionCardActive: {
    borderColor: '#3b82f6',
    backgroundColor: '#1e40af',
  },
  playerSelectionName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 4,
  },
  playerSelectionPosition: {
    fontSize: 12,
    color: '#64748b',
  },
  playerSelectionBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#dc2626',
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  backButton: {
    flex: 1,
    backgroundColor: '#374151',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  backButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flex: 2,
    backgroundColor: '#3b82f6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  nextButtonDisabled: {
    opacity: 0.6,
  },
  nextButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});
