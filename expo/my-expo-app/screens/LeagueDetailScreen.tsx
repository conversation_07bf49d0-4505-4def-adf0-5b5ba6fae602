// screens/LeagueDetailScreen.tsx - League detail screen for mobile
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl,
  Clipboard,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useLeagueStore, League, LeagueUser } from '../store/useLeagueStore';
import { useRouter, useLocalSearchParams } from 'expo-router';
import Header from '../components/Header';

export default function LeagueDetailScreen() {
  const { user } = useAuth();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [refreshing, setRefreshing] = useState(false);

  const {
    leagues,
    loading,
    error,
    fetchLeagues,
    leaveLeague,
    setCopiedLeagueId,
    copiedLeagueId,
  } = useLeagueStore();

  // Find current league
  const currentLeague = leagues.find(league => league.id === id);

  // Initialize leagues if not loaded
  useEffect(() => {
    if (leagues.length === 0) {
      fetchLeagues();
    }
  }, []);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchLeagues();
    setRefreshing(false);
  };

  // Check if user is admin of the league
  const isUserAdmin = (league: League) => {
    return league.admins.some(admin => admin.id === user?.id);
  };

  // Check if user is member of the league
  const isUserMember = (league: League) => {
    return league.members.some(member => member.id === user?.id);
  };

  // Handle leave league
  const handleLeaveLeague = () => {
    if (!currentLeague) return;

    Alert.alert(
      'Leave League',
      `Are you sure you want to leave "${currentLeague.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: async () => {
            const success = await leaveLeague(currentLeague.id);
            if (success) {
              Alert.alert('Success', 'Left league successfully!', [
                { text: 'OK', onPress: () => router.back() }
              ]);
            } else if (error) {
              Alert.alert('Error', error);
            }
          },
        },
      ]
    );
  };

  // Copy league code
  const copyLeagueCode = () => {
    if (!currentLeague) return;
    
    Clipboard.setString(currentLeague.code);
    setCopiedLeagueId(currentLeague.id);
    Alert.alert('Copied', 'League code copied to clipboard!');
    
    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopiedLeagueId(null);
    }, 2000);
  };

  // Navigate to manage league (admin only)
  const navigateToManage = () => {
    if (!currentLeague) return;
    router.push(`/leagues/${currentLeague.id}/manage`);
  };

  // Render member item
  const renderMember = (member: LeagueUser, index: number) => {
    const isAdmin = currentLeague?.admins.some(admin => admin.id === member.id);
    const isCurrentUser = member.id === user?.id;

    return (
      <View key={member.id} style={styles.memberItem}>
        <View style={styles.memberRank}>
          <Text style={styles.memberRankText}>{index + 1}</Text>
        </View>
        
        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>
            {member.name} {isCurrentUser && '(You)'}
          </Text>
          <Text style={styles.memberEmail}>{member.email}</Text>
        </View>
        
        <View style={styles.memberStats}>
          <Text style={styles.memberPoints}>
            {member.totalPoints || 0} pts
          </Text>
          {isAdmin && (
            <View style={styles.adminBadge}>
              <Text style={styles.adminBadgeText}>Admin</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  if (loading && !currentLeague) {
    return (
      <SafeAreaView style={styles.container}>
        <Header title="League" showBack />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading league...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!currentLeague) {
    return (
      <SafeAreaView style={styles.container}>
        <Header title="League" showBack />
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>League not found</Text>
          <Text style={styles.errorDescription}>
            This league may have been deleted or you may not have access to it.
          </Text>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header title={currentLeague.name} showBack />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* League Info */}
        <View style={styles.leagueInfoContainer}>
          <View style={styles.leagueHeader}>
            <Text style={styles.leagueName}>{currentLeague.name}</Text>
            <Text style={styles.leagueCode}>Code: {currentLeague.code}</Text>
          </View>
          
          <View style={styles.leagueStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{currentLeague._count.members}</Text>
              <Text style={styles.statLabel}>Members</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{currentLeague.type}</Text>
              <Text style={styles.statLabel}>Type</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{currentLeague.status}</Text>
              <Text style={styles.statLabel}>Status</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.copyButton}
            onPress={copyLeagueCode}
          >
            <Text style={styles.copyButtonText}>
              {copiedLeagueId === currentLeague.id ? 'Copied!' : 'Copy Code'}
            </Text>
          </TouchableOpacity>
          
          {isUserAdmin(currentLeague) && (
            <TouchableOpacity
              style={styles.manageButton}
              onPress={navigateToManage}
            >
              <Text style={styles.manageButtonText}>Manage</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={styles.leaveButton}
            onPress={handleLeaveLeague}
          >
            <Text style={styles.leaveButtonText}>Leave</Text>
          </TouchableOpacity>
        </View>

        {/* Error Message */}
        {error && (
          <View style={styles.errorMessageContainer}>
            <Text style={styles.errorMessageText}>{error}</Text>
          </View>
        )}

        {/* Members List */}
        <View style={styles.membersContainer}>
          <Text style={styles.membersTitle}>Leaderboard</Text>
          
          {currentLeague.members.length === 0 ? (
            <View style={styles.emptyMembersContainer}>
              <Text style={styles.emptyMembersText}>No members yet</Text>
            </View>
          ) : (
            <View style={styles.membersList}>
              {currentLeague.members
                .sort((a, b) => (b.totalPoints || 0) - (a.totalPoints || 0))
                .map((member, index) => renderMember(member, index))}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f172a',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    color: '#64748b',
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  errorDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  leagueInfoContainer: {
    margin: 20,
    backgroundColor: '#1e293b',
    borderWidth: 1,
    borderColor: '#334155',
    borderRadius: 12,
    padding: 20,
  },
  leagueHeader: {
    marginBottom: 16,
  },
  leagueName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  leagueCode: {
    fontSize: 16,
    color: '#64748b',
  },
  leagueStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748b',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 8,
  },
  copyButton: {
    flex: 1,
    backgroundColor: '#374151',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  copyButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  manageButton: {
    flex: 1,
    backgroundColor: '#3b82f6',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  manageButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  leaveButton: {
    flex: 1,
    backgroundColor: '#dc2626',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  leaveButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  errorMessageContainer: {
    marginHorizontal: 20,
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#dc2626',
    borderRadius: 8,
  },
  errorMessageText: {
    color: '#ffffff',
    fontSize: 14,
    textAlign: 'center',
  },
  membersContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  membersTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 16,
  },
  emptyMembersContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyMembersText: {
    color: '#64748b',
    fontSize: 16,
  },
  membersList: {
    gap: 8,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1e293b',
    borderWidth: 1,
    borderColor: '#334155',
    borderRadius: 12,
    padding: 16,
  },
  memberRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#3b82f6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  memberRankText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 2,
  },
  memberEmail: {
    fontSize: 14,
    color: '#64748b',
  },
  memberStats: {
    alignItems: 'flex-end',
  },
  memberPoints: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#10b981',
    marginBottom: 4,
  },
  adminBadge: {
    backgroundColor: '#dc2626',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  adminBadgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
