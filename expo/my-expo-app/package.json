{"name": "my-expo-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"android": "expo start --android", "ios": "expo start --ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web", "prisma:generate": "npx prisma generate", "prisma:studio": "npx prisma studio", "dev": "expo start --clear", "build": "next build", "next:dev": "next dev", "next:start": "next start"}, "dependencies": {"@prisma/client": "^6.13.0", "@react-native-async-storage/async-storage": "^2.1.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "@upstash/redis": "^1.35.3", "ajv": "^8.17.1", "expo": "^53.0.20", "expo-auth-session": "^6.2.1", "expo-constants": "~17.1.7", "expo-crypto": "^14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "nativewind": "latest", "next": "^15.4.6", "prisma": "^6.13.0", "react": "19.0.0", "react-dom": "^18.3.1", "react-native": "0.79.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.21.0", "zustand": "^5.0.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.0", "typescript": "~5.8.3"}, "private": true}