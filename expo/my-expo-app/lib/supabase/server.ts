import { createServerClient } from '@supabase/ssr'

export async function createClient() {
  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
  const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

  // For Expo Router API routes, we don't have access to cookies in the same way
  // This is a simplified version for server-side operations
  return createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        getAll() {
          return []
        },
        setAll() {
          // No-op for Expo Router API routes
        },
      },
    }
  )
}
