// lib/cache.ts
import { Redis } from "@upstash/redis";

const redis = Redis.fromEnv();

export class CacheService {
  static async getPlayers<T>(fetchFn: () => Promise<T>) {
    const cacheKey = "players:data:v1";

    try {
      // Check cache
      const cached = await redis.get<T>(cacheKey);
      if (cached) {
        return cached;
      }
    } catch (error) {
      console.warn("Cache read failed, falling back to direct fetch:", error);
    }

    // Fetch fresh data
    const data = await fetchFn();

    try {
      // Cache indefinitely - manual invalidation only
      await redis.set(cacheKey, data);
    } catch (error) {
      console.warn("Cache write failed:", error);
    }

    return data;
  }

  static async getTeams<T>(fetchFn: () => Promise<T>) {
    const cacheKey = "teams:data:v1";

    try {
      // Check cache
      const cached = await redis.get<T>(cacheKey);
      if (cached) {
        return cached;
      }
    } catch (error) {
      console.warn("Cache read failed, falling back to direct fetch:", error);
    }

    // Fetch fresh data
    const data = await fetchFn();

    try {
      // Cache indefinitely - manual invalidation only
      await redis.set(cacheKey, data);
    } catch (error) {
      console.warn("Cache write failed:", error);
    }

    return data;
  }

  static async getGameweeks<T>(fetchFn: () => Promise<T>) {
    const cacheKey = "gameweeks:data:v1";

    try {
      // Check cache
      const cached = await redis.get<T>(cacheKey);
      if (cached) {
        return cached;
      }
    } catch (error) {
      console.warn("Cache read failed, falling back to direct fetch:", error);
    }

    // Fetch fresh data
    const data = await fetchFn();

    try {
      // Cache indefinitely - manual invalidation only
      await redis.set(cacheKey, data);
    } catch (error) {
      console.warn("Cache write failed:", error);
    }

    return data;
  }

  static async getFixtures<T>(fetchFn: () => Promise<T>) {
    const cacheKey = "fixtures:data:v1";

    try {
      // Check cache
      const cached = await redis.get<T>(cacheKey);
      if (cached) {
        return cached;
      }
    } catch (error) {
      console.warn("Cache read failed, falling back to direct fetch:", error);
    }

    // Fetch fresh data
    const data = await fetchFn();

    try {
      // Cache indefinitely - manual invalidation only
      await redis.set(cacheKey, data);
    } catch (error) {
      console.warn("Cache write failed:", error);
    }

    return data;
  }

  static async getLeagues<T>(fetchFn: () => Promise<T>, userId?: string) {
    if (!userId) return;
    const cacheKey = `leagues:user:${userId}:v1`;

    try {
      // Check cache
      const cached = await redis.get<T>(cacheKey);
      if (cached) {
        return cached;
      }
    } catch (error) {
      console.warn("Cache read failed, falling back to direct fetch:", error);
    }

    // Fetch fresh data
    const data = await fetchFn();

    try {
      // Cache indefinitely - manual invalidation only
      await redis.set(cacheKey, data);
    } catch (error) {
      console.warn("Cache write failed:", error);
    }

    return data;
  }

  // Cache invalidation methods
  static async invalidatePlayersCache() {
    try {
      await redis.del("players:data:v1");
    } catch (error) {
      console.warn("Cache invalidation failed:", error);
    }
  }

  static async invalidateTeamsCache() {
    try {
      await redis.del("teams:data:v1");
    } catch (error) {
      console.warn("Cache invalidation failed:", error);
    }
  }

  static async invalidateGameweeksCache() {
    try {
      await redis.del("gameweeks:data:v1");
    } catch (error) {
      console.warn("Cache invalidation failed:", error);
    }
  }

  static async invalidateFixturesCache() {
    try {
      await redis.del("fixtures:data:v1");
    } catch (error) {
      console.warn("Cache invalidation failed:", error);
    }
  }

  static async getLeagues<T>(fetchFn: () => Promise<T>, userId?: string) {
    const cacheKey = userId ? `leagues:user:${userId}:v1` : "leagues:data:v1";

    try {
      // Check cache
      const cached = await redis.get<T>(cacheKey);
      if (cached) {
        return cached;
      }
    } catch (error) {
      console.warn("Cache read failed, falling back to direct fetch:", error);
    }

    // Fetch fresh data
    const data = await fetchFn();

    try {
      // Cache indefinitely - manual invalidation only
      await redis.set(cacheKey, data);
    } catch (error) {
      console.warn("Cache write failed:", error);
    }

    return data;
  }

  static async invalidateLeaguesCache() {
    try {
      const keys = await redis.keys("leagues:*");
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.warn("League cache invalidation failed:", error);
    }
  }

  // reset cache script
  static async resetCache() {
    console.log("Resetting cache...");
    CacheService.invalidatePlayersCache();
    CacheService.invalidateTeamsCache();
    CacheService.invalidateGameweeksCache();
    CacheService.invalidateFixturesCache();
    CacheService.invalidateLeaguesCache();
    console.log("Flushing database...");
    try {
      await redis.flushdb();
    } catch (error) {
      console.warn("Cache flush failed:", error);
    }
  }
}
