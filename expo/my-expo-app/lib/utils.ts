// lib/utils.ts - Essential utilities for Expo app
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Transfer validation constants
export const TRANSFER_RULES = {
  MAX_BUDGET: 100,
  MAX_PLAYERS_PER_TEAM: 3,
  TRANSFER_PENALTY_POINTS: -4,
  REQUIRED_STARTERS: 11,
  REQUIRED_SUBS: 4,
  REQUIRED_POSITIONS: { GK: 2, DEF: 5, MID: 5, ATK: 3 },
  STARTER_MIN_NUM: 11,
  SUB_MIN_NUM: 4,
} as const;

// Team validation constants
export const TEAM_RULES = {
  PLAYERS: 15,
  REQUIRED_STARTERS: 11,
  REQUIRED_SUBS: 4,
  REQUIRED_POSITIONS: { GK: 2, DEF: 5, MID: 5, ATK: 3 },
} as const;

// Position order for sorting
const positionOrder = { GK: 1, DEF: 2, MID: 3, ATK: 4 };

export function sortPlayers(
  players: any[],
  sortBy: string = "position"
) {
  if (sortBy === "price") {
    return players.sort(
      (a, b) => Number(b.currentPrice) - Number(a.currentPrice)
    );
  }
  return players.sort((a, b) => {
    if (a.position !== b.position) {
      return positionOrder[a.position] - positionOrder[b.position];
    }
    return a.name.localeCompare(b.name);
  });
}

// Get current period based on gameweek number
export function getCurrentPeriod(gwNumber: number): string {
  if (gwNumber <= 15) return "1";
  return "2";
}

// Chip types
export const CHIP = {
  WILDCARD: "WILDCARD",
  BENCH_BOOST: "BENCH_BOOST",
  TRIPLE_CAPTAIN: "TRIPLE_CAPTAIN",
  FREE_HIT: "FREE_HIT",
} as const;
