{"name": "uncrypto", "version": "0.1.3", "description": "Single API for Web Crypto API and Crypto Subtle working in Node.js, Browsers and other runtimes", "repository": "unjs/uncrypto", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"browser": "./dist/crypto.web.mjs", "bun": "./dist/crypto.web.mjs", "deno": "./dist/crypto.web.mjs", "edge-light": "./dist/crypto.web.mjs", "edge-routine": "./dist/crypto.web.mjs", "lagon": "./dist/crypto.web.mjs", "netlify": "./dist/crypto.web.mjs", "react-native": "./dist/crypto.web.mjs", "wintercg": "./dist/crypto.web.mjs", "worker": "./dist/crypto.web.mjs", "workerd": "./dist/crypto.web.mjs", "node": {"require": "./dist/crypto.node.cjs", "import": "./dist/crypto.node.mjs", "types": "./dist/crypto.node.d.ts"}, "require": "./dist/crypto.web.cjs", "import": "./dist/crypto.web.mjs", "types": "./dist/crypto.web.d.ts"}}, "main": "./dist/crypto.node.cjs", "module": "./dist/crypto.web.mjs", "browser": "./dist/crypto.web.mjs", "types": "./dist/crypto.web.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest dev", "lint": "eslint --cache --ext .ts,.js,.mjs,.cjs . && prettier -c src test", "lint:fix": "eslint --cache --ext .ts,.js,.mjs,.cjs . --fix && prettier -c src test -w", "prepack": "pnpm run build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@types/node": "^20.2.5", "@vitest/coverage-v8": "^0.32.0", "changelogen": "^0.5.3", "eslint": "^8.42.0", "eslint-config-unjs": "^0.2.1", "prettier": "^2.8.8", "typescript": "^5.1.3", "unbuild": "^1.2.1", "vitest": "^0.32.0"}, "packageManager": "pnpm@8.6.1"}