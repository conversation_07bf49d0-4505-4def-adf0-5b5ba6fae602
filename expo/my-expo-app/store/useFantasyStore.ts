// store/useFantasyStore.ts - Fantasy store for React Native
import { create } from 'zustand';

// Types
export interface Player {
  id: string;
  name: string;
  position: 'GK' | 'DEF' | 'MID' | 'ATK';
  teamId: string;
  currentPrice: number;
  team?: {
    id: string;
    name: string;
    abbr: string;
    logo: string;
    jersey: string;
  };
  gwStats?: Array<{
    id: string;
    GW: string;
    points: number;
    minutesPlayed: number;
    goalsScored: number;
    assists: number;
  }>;
  totalStats?: {
    points: number;
    goalsScored: number;
    assists: number;
  };
}

export interface GameWeek {
  id: string;
  GW: number;
  season: string;
  isActive: boolean;
  status: string;
}

export interface UserGWSheet {
  id: string;
  userId: string;
  GW: string;
  starters: string[];
  subs: string[];
  captainId?: string;
  viceCaptainId?: string;
  firstSubId?: string;
  secondSubId?: string;
  thirdSubId?: string;
  fourthSubId?: string;
  totalPoints: number;
  transfersMade: number;
}

export interface User {
  id: string;
  email: string;
  name?: string;
  moneyLeft: number;
  transfersLeft: number;
  totalPoints: number;
  hasOnboarded: boolean;
}

export interface Team {
  id: string;
  name: string;
  abbr: string;
  logo: string;
  jersey: string;
}

export type ViewMode = 'pitch' | 'list';

interface FantasyState {
  // Loading states
  isLoading: boolean;
  error: string | null;

  // User state
  user: User | null;
  
  // Gameweek state
  activeGameweek: GameWeek | null;
  gameweeks: GameWeek[];
  
  // Player state
  players: Player[];
  teams: Team[];
  
  // Team state
  starters: Player[];
  subs: Player[];
  captainId: string | null;
  viceCaptainId: string | null;
  userGWSheet: UserGWSheet | null;
  
  // View state
  view: ViewMode;
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setUser: (user: User | null) => void;
  setActiveGameweek: (gameweek: GameWeek | null) => void;
  setGameweeks: (gameweeks: GameWeek[]) => void;
  setPlayers: (players: Player[]) => void;
  setTeams: (teams: Team[]) => void;
  setStarters: (starters: Player[]) => void;
  setSubs: (subs: Player[]) => void;
  setCaptainId: (captainId: string | null) => void;
  setViceCaptainId: (viceCaptainId: string | null) => void;
  setUserGWSheet: (sheet: UserGWSheet | null) => void;
  setView: (view: ViewMode) => void;
  
  // API actions
  fetchGameweeks: () => Promise<void>;
  fetchPlayers: () => Promise<void>;
  fetchTeams: () => Promise<void>;
  fetchUserTeam: (userId: string, gameweekId: string) => Promise<void>;
  saveTeam: (userId: string, gameweekId: string) => Promise<boolean>;
  
  // Team management
  handleCaptainChange: (playerId: string) => void;
  handleViceCaptainChange: (playerId: string) => void;
  
  // Reset
  reset: () => void;
}

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8083';

export const useFantasyStore = create<FantasyState>((set, get) => ({
  // Initial state
  isLoading: false,
  error: null,
  user: null,
  activeGameweek: null,
  gameweeks: [],
  players: [],
  teams: [],
  starters: [],
  subs: [],
  captainId: null,
  viceCaptainId: null,
  userGWSheet: null,
  view: 'pitch',

  // Basic setters
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  setUser: (user) => set({ user }),
  setActiveGameweek: (gameweek) => set({ activeGameweek: gameweek }),
  setGameweeks: (gameweeks) => {
    const activeGameweek = gameweeks.find(gw => gw.isActive) || null;
    set({ gameweeks, activeGameweek });
  },
  setPlayers: (players) => set({ players }),
  setTeams: (teams) => set({ teams }),
  setStarters: (starters) => set({ starters }),
  setSubs: (subs) => set({ subs }),
  setCaptainId: (captainId) => set({ captainId }),
  setViceCaptainId: (viceCaptainId) => set({ viceCaptainId }),
  setUserGWSheet: (sheet) => set({ userGWSheet: sheet }),
  setView: (view) => set({ view }),

  // API actions
  fetchGameweeks: async () => {
    try {
      set({ isLoading: true, error: null });
      const response = await fetch(`${API_BASE_URL}/api/gameweek`);
      if (!response.ok) throw new Error('Failed to fetch gameweeks');
      
      const gameweeks = await response.json();
      get().setGameweeks(gameweeks);
    } catch (error: any) {
      set({ error: error.message });
    } finally {
      set({ isLoading: false });
    }
  },

  fetchPlayers: async () => {
    try {
      set({ isLoading: true, error: null });
      const response = await fetch(`${API_BASE_URL}/api/players`);
      if (!response.ok) throw new Error('Failed to fetch players');
      
      const players = await response.json();
      set({ players });
    } catch (error: any) {
      set({ error: error.message });
    } finally {
      set({ isLoading: false });
    }
  },

  fetchTeams: async () => {
    try {
      set({ isLoading: true, error: null });
      const response = await fetch(`${API_BASE_URL}/api/teams`);
      if (!response.ok) throw new Error('Failed to fetch teams');
      
      const teams = await response.json();
      set({ teams });
    } catch (error: any) {
      set({ error: error.message });
    } finally {
      set({ isLoading: false });
    }
  },

  fetchUserTeam: async (userId: string, gameweekId: string) => {
    try {
      set({ isLoading: true, error: null });
      const response = await fetch(`${API_BASE_URL}/api/userGWSheet/${userId}/${gameweekId}`);

      if (response.status === 404) {
        // No team found for this gameweek
        set({
          starters: [],
          subs: [],
          captainId: null,
          viceCaptainId: null,
          userGWSheet: null
        });
        return;
      }

      if (!response.ok) throw new Error('Failed to fetch user team');

      const userGWSheet = await response.json();
      const { players } = get();

      // Convert player IDs to player objects
      const starters = players.filter(p => userGWSheet.starters?.includes(p.id) || []);
      const subs = players.filter(p => userGWSheet.subs?.includes(p.id) || []);

      // Sort starters by position
      starters.sort((a, b) => {
        const order = { GK: 0, DEF: 1, MID: 2, ATK: 3 };
        return order[a.position] - order[b.position];
      });

      // Sort subs (GK first, then others)
      subs.sort((a, b) => {
        if (a.position === 'GK') return -1;
        if (b.position === 'GK') return 1;
        return 0;
      });

      set({
        starters,
        subs,
        captainId: userGWSheet.captainId,
        viceCaptainId: userGWSheet.viceCaptainId,
        userGWSheet,
      });
    } catch (error: any) {
      console.error('Error fetching user team:', error);
      set({ error: error.message });
    } finally {
      set({ isLoading: false });
    }
  },

  saveTeam: async (userId: string, gameweekId: string) => {
    try {
      set({ isLoading: true, error: null });
      const { starters, subs, captainId, viceCaptainId } = get();
      
      const response = await fetch(`${API_BASE_URL}/api/userGWSheet/${userId}/${gameweekId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          starters: starters.map(p => p.id),
          subs: subs.map(p => p.id),
          captainId,
          viceCaptainId,
          firstSubId: subs.find(p => p.position === 'GK')?.id || null,
          secondSubId: subs.filter(p => p.position !== 'GK')[0]?.id || null,
          thirdSubId: subs.filter(p => p.position !== 'GK')[1]?.id || null,
          fourthSubId: subs.filter(p => p.position !== 'GK')[2]?.id || null,
        }),
      });
      
      if (!response.ok) throw new Error('Failed to save team');
      
      return true;
    } catch (error: any) {
      set({ error: error.message });
      return false;
    } finally {
      set({ isLoading: false });
    }
  },

  // Team management
  handleCaptainChange: (playerId: string) => {
    const { captainId, viceCaptainId } = get();
    
    if (captainId === playerId) {
      // Remove captain
      set({ captainId: null });
    } else {
      // Set new captain, remove vice captain if same player
      set({ 
        captainId: playerId,
        viceCaptainId: viceCaptainId === playerId ? null : viceCaptainId
      });
    }
  },

  handleViceCaptainChange: (playerId: string) => {
    const { captainId, viceCaptainId } = get();
    
    if (viceCaptainId === playerId) {
      // Remove vice captain
      set({ viceCaptainId: null });
    } else {
      // Set new vice captain, remove captain if same player
      set({ 
        viceCaptainId: playerId,
        captainId: captainId === playerId ? null : captainId
      });
    }
  },

  // Reset
  reset: () => set({
    isLoading: false,
    error: null,
    user: null,
    activeGameweek: null,
    gameweeks: [],
    players: [],
    teams: [],
    starters: [],
    subs: [],
    captainId: null,
    viceCaptainId: null,
    userGWSheet: null,
    view: 'pitch',
  }),
}));
