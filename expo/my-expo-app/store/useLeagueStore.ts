// store/useLeagueStore.ts - League management store for React Native
import { create } from 'zustand';

export interface LeagueUser {
  id: string;
  name: string;
  email: string;
  totalPoints?: number;
  favoriteClub?: string;
}

export interface League {
  id: string;
  name: string;
  code: string;
  status: 'PRIVATE' | 'PUBLIC';
  type: 'STANDING' | 'HEAD_TO_HEAD';
  startFromGW?: string;
  admins: LeagueUser[];
  members: LeagueUser[];
  _count: {
    members: number;
  };
}

interface LeagueState {
  // State
  leagues: League[];
  currentLeague: League | null;
  loading: boolean;
  error: string | null;

  // UI State
  searchTerm: string;
  createDialogOpen: boolean;
  joinDialogOpen: boolean;
  newLeagueName: string;
  joinCode: string;
  copiedLeagueId: string | null;

  // Actions
  setLeagues: (leagues: League[]) => void;
  setCurrentLeague: (league: League | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // UI Actions
  setSearchTerm: (term: string) => void;
  setCreateDialogOpen: (open: boolean) => void;
  setJoinDialogOpen: (open: boolean) => void;
  setNewLeagueName: (name: string) => void;
  setJoinCode: (code: string) => void;
  setCopiedLeagueId: (id: string | null) => void;

  // API Actions
  fetchLeagues: () => Promise<void>;
  createLeague: (name: string) => Promise<boolean>;
  joinLeague: (code: string) => Promise<boolean>;
  leaveLeague: (leagueId: string) => Promise<boolean>;
  updateLeague: (leagueId: string, name: string) => Promise<boolean>;
  deleteLeague: (leagueId: string) => Promise<boolean>;
  removeMember: (leagueId: string, memberId: string) => Promise<boolean>;
}

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8082';

const initialState = {
  leagues: [],
  currentLeague: null,
  loading: false,
  error: null,
  searchTerm: '',
  createDialogOpen: false,
  joinDialogOpen: false,
  newLeagueName: '',
  joinCode: '',
  copiedLeagueId: null,
};

export const useLeagueStore = create<LeagueState>((set, get) => ({
  ...initialState,

  // Basic setters
  setLeagues: (leagues) => set({ leagues }),
  setCurrentLeague: (league) => set({ currentLeague: league }),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),

  // UI setters
  setSearchTerm: (searchTerm) => set({ searchTerm }),
  setCreateDialogOpen: (createDialogOpen) => set({ createDialogOpen }),
  setJoinDialogOpen: (joinDialogOpen) => set({ joinDialogOpen }),
  setNewLeagueName: (newLeagueName) => set({ newLeagueName }),
  setJoinCode: (joinCode) => set({ joinCode }),
  setCopiedLeagueId: (copiedLeagueId) => set({ copiedLeagueId }),

  // API Actions
  fetchLeagues: async () => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`${API_BASE_URL}/api/leagues`);
      
      if (response.ok) {
        const leagues = await response.json();
        set({ leagues, loading: false });
      } else {
        const error = await response.json();
        set({ 
          error: error.error || 'Failed to fetch leagues',
          loading: false 
        });
      }
    } catch (error) {
      console.error('Error fetching leagues:', error);
      set({ 
        error: 'Network error. Please try again.',
        loading: false 
      });
    }
  },

  createLeague: async (name: string) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`${API_BASE_URL}/api/leagues`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name,
          status: 'PRIVATE',
          type: 'STANDING',
        }),
      });

      if (response.ok) {
        get().fetchLeagues();
        set({ loading: false });
        return true;
      } else {
        const error = await response.json();
        set({
          error: error.error || 'Failed to create league',
          loading: false,
        });
        return false;
      }
    } catch (error) {
      console.error('Error creating league:', error);
      set({
        error: 'Network error. Please try again.',
        loading: false,
      });
      return false;
    }
  },

  joinLeague: async (code: string) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`${API_BASE_URL}/api/leagues/join?code=${encodeURIComponent(code)}`, {
        method: 'PUT',
      });

      if (response.ok) {
        get().fetchLeagues();
        set({ loading: false });
        return true;
      } else {
        const error = await response.json();
        set({
          error: error.error || 'Failed to join league',
          loading: false,
        });
        return false;
      }
    } catch (error) {
      console.error('Error joining league:', error);
      set({
        error: 'Network error. Please try again.',
        loading: false,
      });
      return false;
    }
  },

  leaveLeague: async (leagueId: string) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`${API_BASE_URL}/api/leagues/${leagueId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        get().fetchLeagues();
        set({ loading: false });
        return true;
      } else {
        const error = await response.json();
        set({
          error: error.error || 'Failed to leave league',
          loading: false,
        });
        return false;
      }
    } catch (error) {
      console.error('Error leaving league:', error);
      set({
        error: 'Network error. Please try again.',
        loading: false,
      });
      return false;
    }
  },

  updateLeague: async (leagueId: string, name: string) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`${API_BASE_URL}/api/leagues/${leagueId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name }),
      });

      if (response.ok) {
        get().fetchLeagues();
        set({ loading: false });
        return true;
      } else {
        const error = await response.json();
        set({
          error: error.error || 'Failed to update league',
          loading: false,
        });
        return false;
      }
    } catch (error) {
      console.error('Error updating league:', error);
      set({
        error: 'Network error. Please try again.',
        loading: false,
      });
      return false;
    }
  },

  deleteLeague: async (leagueId: string) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`${API_BASE_URL}/api/leagues/${leagueId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        get().fetchLeagues();
        set({ loading: false });
        return true;
      } else {
        const error = await response.json();
        set({
          error: error.error || 'Failed to delete league',
          loading: false,
        });
        return false;
      }
    } catch (error) {
      console.error('Error deleting league:', error);
      set({
        error: 'Network error. Please try again.',
        loading: false,
      });
      return false;
    }
  },

  removeMember: async (leagueId: string, memberId: string) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`${API_BASE_URL}/api/leagues/${leagueId}/members/${memberId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        get().fetchLeagues();
        set({ loading: false });
        return true;
      } else {
        const error = await response.json();
        set({
          error: error.error || 'Failed to remove member',
          loading: false,
        });
        return false;
      }
    } catch (error) {
      console.error('Error removing member:', error);
      set({
        error: 'Network error. Please try again.',
        loading: false,
      });
      return false;
    }
  },
}));
